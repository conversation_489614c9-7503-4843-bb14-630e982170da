"""
Production Ready Contact + Business Extractor
Clean, validated extraction with status tracking and contact form detection.
"""

import asyncio
import pandas as pd
import re
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


class ProductionReadyExtractor(PerfectContactExtractor):
    """Production-ready extractor with validation and status tracking."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings."""
        super().__init__(batch_size, max_concurrent)
    
    async def extract_production_data(self, urls: list) -> list:
        """Extract production-ready contact + business data."""
        print(f"🚀 Starting PRODUCTION extraction for {len(urls)} URLs...")
        print(f"   ✅ VALIDATED: All emails/phones validated")
        print(f"   📊 STATUS TRACKING: Clear found/not_found status")
        print(f"   📝 CONTACT FORMS: Detection included")
        print(f"   🏢 BUSINESS INFO: Company names, descriptions, services")
        
        # Use the proven perfect extraction method
        results = await self.extract_perfect(urls)
        
        # Clean and enhance each result
        print(f"🔧 Cleaning and validating {len(results)} results...")
        
        production_results = []
        for i, result in enumerate(results):
            if 'error' not in result:
                cleaned_result = await self._clean_and_validate_result(result)
                production_results.append(cleaned_result)
            else:
                production_results.append(result)
            
            if (i + 1) % 10 == 0:
                print(f"   ✅ Processed {i + 1}/{len(results)} results")
        
        return production_results
    
    async def _clean_and_validate_result(self, result: dict) -> dict:
        """Clean and validate a single result."""
        url = result.get('url', '')
        
        # Clean email data
        email_data = self._clean_email_data(result.get('email'))
        
        # Clean phone data
        phone_data = self._clean_phone_data(result.get('phone'))
        
        # Clean social media data
        social_media = self._clean_social_media_data(result.get('social_media'))
        
        # Get business info and contact form detection
        business_info = await self._get_business_info(url)
        contact_form_detected = business_info.pop('contact_form_detected', False)
        
        # Create clean production result
        production_result = {
            'url': url,
            'timestamp': result.get('timestamp', datetime.now().isoformat()),
            # VALIDATED CONTACT DATA
            'email': email_data['email'],
            'email_confidence': email_data['confidence'],
            'email_status': email_data['status'],
            'phone': phone_data['phone'],
            'phone_confidence': phone_data['confidence'],
            'phone_status': phone_data['status'],
            'social_media': social_media,
            'contact_form_detected': contact_form_detected,
            'pages_checked': result.get('pages_checked', 0),
            # BUSINESS INTELLIGENCE
            **business_info
        }
        
        return production_result
    
    def _clean_email_data(self, email_raw) -> dict:
        """Clean and validate email data."""
        email = ''
        confidence = 0
        status = 'not_found'
        
        # Extract email from various formats
        if isinstance(email_raw, dict):
            email = email_raw.get('email', '')
            confidence = email_raw.get('confidence', 0)
        elif isinstance(email_raw, str) and email_raw and email_raw != 'None':
            email = email_raw
            confidence = 0.8
        
        # Validate email
        if email and self._is_valid_email(email) and self._is_business_email(email):
            status = 'found'
        else:
            email = ''
            confidence = 0
            status = 'not_found'
        
        return {
            'email': email,
            'confidence': confidence,
            'status': status
        }
    
    def _clean_phone_data(self, phone_raw) -> dict:
        """Clean and validate phone data."""
        phone = ''
        confidence = 0
        status = 'not_found'
        
        # Extract phone from various formats
        if isinstance(phone_raw, dict):
            phone = phone_raw.get('phone', '')
            confidence = phone_raw.get('confidence', 0)
        elif isinstance(phone_raw, str) and phone_raw and phone_raw != 'None':
            phone = phone_raw
            confidence = 0.8
        
        # Clean and validate phone
        if phone:
            clean_phone = self._format_phone_number(phone)
            if clean_phone and self._is_valid_phone(clean_phone):
                phone = clean_phone
                status = 'found'
            else:
                phone = ''
                confidence = 0
                status = 'not_found'
        
        return {
            'phone': phone,
            'confidence': confidence,
            'status': status
        }
    
    def _clean_social_media_data(self, social_raw) -> str:
        """Clean social media data."""
        if not social_raw:
            return ''
        
        if isinstance(social_raw, dict):
            platform = social_raw.get('platform', '')
            url = social_raw.get('url', '')
            if platform and url:
                return f"{platform}: {url}"
            return url
        
        return str(social_raw) if social_raw != 'None' else ''
    
    async def _get_business_info(self, url: str) -> dict:
        """Get business information and detect contact forms."""
        try:
            from crawl4ai import AsyncWebCrawler
            async with AsyncWebCrawler() as crawler:
                from crawl4ai import CrawlerRunConfig
                config = CrawlerRunConfig()
                result = await crawler.arun(url=url, config=config)
                
                if result.success and result.cleaned_html:
                    return self._extract_business_from_html(result.cleaned_html)
                
        except Exception as e:
            print(f"⚠️  Business info extraction failed for {url}: {str(e)}")
        
        return self._empty_business_info()
    
    def _extract_business_from_html(self, html_content: str) -> dict:
        """Extract business information from HTML."""
        business_info = {}
        
        # Page title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        business_info['page_title'] = title_match.group(1).strip() if title_match else ''
        
        # Meta description
        meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        business_info['meta_description'] = meta_match.group(1).strip() if meta_match else ''
        
        # Company name
        business_info['company_name'] = self._extract_company_name(html_content)
        
        # About text
        business_info['about_text'] = self._extract_about_text(html_content)
        
        # Services text
        business_info['services_text'] = self._extract_services_text(html_content)
        
        # Key headings
        business_info['key_headings'] = self._extract_headings(html_content)
        
        # Business summary
        business_info['business_summary'] = self._generate_business_summary(business_info)
        
        # Contact form detection
        business_info['contact_form_detected'] = self._detect_contact_form(html_content)
        
        return business_info
    
    def _extract_company_name(self, html_content: str) -> str:
        """Extract company name."""
        patterns = [
            r'<h1[^>]*>([^<]+)</h1>',
            r'<title[^>]*>([^<|\\-]+)',
            r'class=["\'][^"\']*(?:company|brand|business)[^"\']*["\'][^>]*>([^<]+)',
            r'<img[^>]*alt=["\']([^"\']*(?:logo|company|brand)[^"\']*)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                candidate = match.strip()
                if (len(candidate) > 2 and len(candidate) < 100 and 
                    not any(word in candidate.lower() for word in ['home', 'welcome', 'contact', 'about', 'menu'])):
                    return candidate
        
        return ''
    
    def _extract_about_text(self, html_content: str) -> str:
        """Extract about text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)about\s+us[^<]*<[^>]*>([^<]+)',
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']',
        ]
        
        about_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 20 and len(text) < 500:
                    about_texts.append(text)
        
        return ' '.join(about_texts[:2])[:400] if about_texts else ''
    
    def _extract_services_text(self, html_content: str) -> str:
        """Extract services text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*service[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)(?:our\s+)?(?:services|products)[^<]*<[^>]*>([^<]+)',
        ]
        
        service_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 10 and len(text) < 300:
                    service_texts.append(text)
        
        return ' '.join(service_texts[:2])[:300] if service_texts else ''
    
    def _extract_headings(self, html_content: str) -> str:
        """Extract key headings."""
        heading_matches = re.findall(r'<h[1-3][^>]*>([^<]+)</h[1-3]>', html_content, re.IGNORECASE)
        headings = []
        for heading in heading_matches:
            clean_heading = re.sub(r'\s+', ' ', heading.strip())
            if len(clean_heading) > 2 and len(clean_heading) < 80:
                headings.append(clean_heading)
        
        return ' | '.join(headings[:6])
    
    def _generate_business_summary(self, business_info: dict) -> str:
        """Generate business summary."""
        parts = []
        
        if business_info.get('company_name'):
            parts.append(business_info['company_name'])
        
        if business_info.get('meta_description'):
            parts.append(business_info['meta_description'][:100])
        elif business_info.get('about_text'):
            parts.append(business_info['about_text'][:100])
        
        return ' | '.join(parts)
    
    def _detect_contact_form(self, html_content: str) -> bool:
        """Detect contact forms."""
        patterns = [
            r'<form[^>]*(?:contact|email|inquiry)[^>]*>',
            r'<input[^>]*type=["\']email["\']',
            r'<textarea[^>]*(?:message|inquiry)[^>]*>',
        ]
        
        for pattern in patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return True
        return False
    
    def _empty_business_info(self) -> dict:
        """Return empty business info."""
        return {
            'page_title': '',
            'meta_description': '',
            'company_name': '',
            'about_text': '',
            'services_text': '',
            'key_headings': '',
            'business_summary': '',
            'contact_form_detected': False
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        if not email or len(email) < 5:
            return False
        pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
        return bool(re.match(pattern, email))
    
    def _is_business_email(self, email: str) -> bool:
        """Check if email is business email."""
        excluded_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
        domain = email.split('@')[1].lower() if '@' in email else ''
        return domain not in excluded_domains
    
    def _format_phone_number(self, phone: str) -> str:
        """Format phone number."""
        digits = re.sub(r'[^\d+]', '', phone)
        
        if digits.startswith('1') and len(digits) == 11:
            digits = digits[1:]
        
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif digits.startswith('+'):
            return digits
        
        return phone
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False
        digits_only = re.sub(r'[^\d]', '', phone)
        return len(digits_only) >= 10 and len(digits_only) <= 15
    
    def export_production_to_csv(self, results: list, filename: str):
        """Export production results to CSV."""
        if not results:
            print("❌ No results to export")
            return
        
        flattened = []
        for result in results:
            if 'error' not in result:
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    # CONTACT DATA
                    'email': result.get('email', ''),
                    'email_confidence': result.get('email_confidence', ''),
                    'email_status': result.get('email_status', ''),
                    'phone': result.get('phone', ''),
                    'phone_confidence': result.get('phone_confidence', ''),
                    'phone_status': result.get('phone_status', ''),
                    'social_media': result.get('social_media', ''),
                    'contact_form_detected': result.get('contact_form_detected', False),
                    'pages_checked': result.get('pages_checked', ''),
                    # BUSINESS DATA
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })
        
        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Production results exported to: {filename}")


async def test_production_extractor():
    """Test the production extractor."""
    print("🚀 TESTING PRODUCTION-READY EXTRACTOR")
    print("=" * 60)
    print("Clean, validated extraction with status tracking!")
    
    # Load test URLs
    try:
        df = pd.read_csv('first_100_prospects_20250915_002842.csv')
        urls = df['website'].dropna().tolist()[:20]
        print(f"✅ Testing with {len(urls)} prospect URLs")
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return
    
    # Create production extractor
    extractor = ProductionReadyExtractor(batch_size=20, max_concurrent=30)
    
    start_time = datetime.now()
    
    # Extract production data
    results = await extractor.extract_production_data(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    successful = len([r for r in results if 'error' not in r])
    
    print(f"\n📊 PRODUCTION EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    
    if successful > 0:
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        contact_forms = len([r for r in results if 'error' not in r and r.get('contact_form_detected')])
        
        print(f"\n🎯 PRODUCTION CONTACT EXTRACTION:")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   📝 Contact forms: {contact_forms}/{successful} ({contact_forms/successful*100:.1f}%)")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"production_extraction_test_{timestamp}.csv"
    extractor.export_production_to_csv(results, filename)
    
    print(f"\n🎉 PRODUCTION EXTRACTION COMPLETE!")
    print(f"💾 Results saved to: {filename}")
    print(f"🚀 Ready for full dataset processing!")


if __name__ == "__main__":
    asyncio.run(test_production_extractor())
