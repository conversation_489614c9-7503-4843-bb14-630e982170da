"""
Enhanced Business Information Extractor
Extracts comprehensive business information including about sections, services, and company details.
"""

import asyncio
import pandas as pd
import re
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor
from crawl4ai import CrawlerRunConfig, JsonCssExtractionStrategy


class EnhancedBusinessExtractor(PerfectContactExtractor):
    """Enhanced extractor that captures comprehensive business information."""
    
    def __init__(self, batch_size: int = 25, max_concurrent: int = 20):
        """Initialize with enhanced extraction capabilities."""
        super().__init__(batch_size, max_concurrent)
    
    def _get_enhanced_extraction_schema(self):
        """Enhanced schema that extracts business information along with contacts."""
        return {
            "name": "Enhanced Business Information",
            "baseSelector": "body",
            "fields": [
                # CONTACT INFORMATION (existing)
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email], [href*='@'], .email, .contact-email",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "phones",
                    "selector": "a[href^='tel:'], [data-phone], .phone, .contact-phone, .telephone",
                    "type": "list",
                    "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='x.com'], a[href*='linkedin.com'], a[href*='youtube.com'], a[href*='tiktok.com'], a[href*='pinterest.com'], a[href*='yelp.com'], .social-links a, .social-media a, [class*='social'] a",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                },
                
                # BUSINESS INFORMATION (new)
                {
                    "name": "page_title",
                    "selector": "title",
                    "type": "text"
                },
                {
                    "name": "meta_description",
                    "selector": "meta[name='description']",
                    "type": "attribute",
                    "attribute": "content"
                },
                {
                    "name": "company_name",
                    "selector": "h1, .company-name, .business-name, .brand-name, .logo-text, [class*='company'], [class*='brand']",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "about_sections",
                    "selector": "[class*='about'], [id*='about'], .about-us, .about-section, .company-info, .our-story, .who-we-are, section[class*='about'], div[class*='about']",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "services_sections",
                    "selector": "[class*='service'], [id*='service'], .services, .what-we-do, .offerings, .products, [class*='product'], .solutions, section[class*='service'], div[class*='service']",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "mission_vision",
                    "selector": "[class*='mission'], [class*='vision'], [class*='values'], .mission, .vision, .values, .purpose, .why-us",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "business_description",
                    "selector": ".description, .intro, .overview, .summary, .tagline, .subtitle, .lead, p:first-of-type",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "industry_keywords",
                    "selector": ".industry, .category, .type, .sector, .field, [class*='industry'], [class*='category']",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "location_info",
                    "selector": ".address, .location, [class*='address'], [class*='location'], .contact-info address, .footer address",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "headings",
                    "selector": "h1, h2, h3",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                }
            ]
        }
    
    async def _extract_from_page_enhanced(self, crawler, url: str) -> dict:
        """Enhanced extraction with business information."""
        try:
            # Use enhanced schema
            schema = self._get_enhanced_extraction_schema()
            strategy = JsonCssExtractionStrategy(schema, verbose=False)
            config = CrawlerRunConfig(extraction_strategy=strategy)

            result = await crawler.arun(url=url, config=config)

            if result.success and result.extracted_content:
                import json
                extracted_data = json.loads(result.extracted_content)

                # Handle case where extracted_data is a list (take first item)
                if isinstance(extracted_data, list) and len(extracted_data) > 0:
                    extracted_data = extracted_data[0]
                elif isinstance(extracted_data, list):
                    extracted_data = {}

                # Process the extracted data
                processed_data = self._process_business_data(extracted_data, url)
                print(f"✅ Successfully extracted from {url}: {len(str(processed_data))} chars")
                return processed_data
            else:
                print(f"❌ No content extracted from {url}: success={result.success}")
                return {"url": url, "error": "No content extracted"}

        except Exception as e:
            print(f"❌ Extraction failed for {url}: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"url": url, "error": f"Extraction failed: {str(e)}"}
    
    def _process_business_data(self, raw_data: dict, url: str) -> dict:
        """Process and clean the extracted business data."""
        processed = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
        }
        
        # Process contact information (existing logic)
        processed.update(self._process_contact_info(raw_data))
        
        # Process business information (new)
        processed.update(self._process_business_info(raw_data))
        
        return processed
    
    def _process_contact_info(self, raw_data: dict) -> dict:
        """Process contact information (emails, phones, social)."""
        contact_info = {}
        
        # Process emails
        emails = raw_data.get('emails', [])
        if emails:
            best_email = self._find_best_email([e.get('email', '') for e in emails])
            if best_email:
                contact_info['email'] = best_email
                contact_info['email_confidence'] = 0.9
        
        # Process phones
        phones = raw_data.get('phones', [])
        if phones:
            best_phone = self._find_best_phone([p.get('phone', '') for p in phones])
            if best_phone:
                contact_info['phone'] = best_phone
                contact_info['phone_confidence'] = 0.8
        
        # Process social media
        social_links = raw_data.get('social', [])
        if social_links:
            best_social = self._find_best_social([s.get('url', '') for s in social_links])
            if best_social:
                contact_info['social_media'] = best_social
        
        return contact_info
    
    def _process_business_info(self, raw_data: dict) -> dict:
        """Process business information."""
        business_info = {}
        
        # Page title and meta description
        business_info['page_title'] = raw_data.get('page_title', '').strip()
        business_info['meta_description'] = raw_data.get('meta_description', '').strip()
        
        # Company name
        company_names = [c.get('text', '').strip() for c in raw_data.get('company_name', []) if c.get('text', '').strip()]
        business_info['company_name'] = company_names[0] if company_names else ''
        
        # About sections
        about_texts = [a.get('text', '').strip() for a in raw_data.get('about_sections', []) if a.get('text', '').strip()]
        business_info['about_text'] = self._combine_and_clean_text(about_texts, max_length=1000)
        
        # Services
        service_texts = [s.get('text', '').strip() for s in raw_data.get('services_sections', []) if s.get('text', '').strip()]
        business_info['services_text'] = self._combine_and_clean_text(service_texts, max_length=800)
        
        # Mission/Vision
        mission_texts = [m.get('text', '').strip() for m in raw_data.get('mission_vision', []) if m.get('text', '').strip()]
        business_info['mission_vision_text'] = self._combine_and_clean_text(mission_texts, max_length=500)
        
        # Business description
        desc_texts = [d.get('text', '').strip() for d in raw_data.get('business_description', []) if d.get('text', '').strip()]
        business_info['business_description'] = self._combine_and_clean_text(desc_texts, max_length=500)
        
        # Industry keywords
        industry_texts = [i.get('text', '').strip() for i in raw_data.get('industry_keywords', []) if i.get('text', '').strip()]
        business_info['industry_keywords'] = ', '.join(industry_texts[:5])  # Top 5 keywords
        
        # Location
        location_texts = [l.get('text', '').strip() for l in raw_data.get('location_info', []) if l.get('text', '').strip()]
        business_info['location_text'] = self._combine_and_clean_text(location_texts, max_length=200)
        
        # Key headings
        headings = [h.get('text', '').strip() for h in raw_data.get('headings', []) if h.get('text', '').strip()]
        business_info['key_headings'] = ' | '.join(headings[:10])  # Top 10 headings
        
        # Generate business summary
        business_info['business_summary'] = self._generate_business_summary(business_info)
        
        return business_info
    
    def _combine_and_clean_text(self, texts: list, max_length: int = 500) -> str:
        """Combine and clean text snippets."""
        if not texts:
            return ''
        
        # Combine texts
        combined = ' '.join(texts)
        
        # Clean up
        combined = re.sub(r'\s+', ' ', combined)  # Multiple spaces to single
        combined = re.sub(r'\n+', ' ', combined)  # Newlines to spaces
        combined = combined.strip()
        
        # Truncate if too long
        if len(combined) > max_length:
            combined = combined[:max_length].rsplit(' ', 1)[0] + '...'
        
        return combined
    
    def _generate_business_summary(self, business_info: dict) -> str:
        """Generate a concise business summary."""
        summary_parts = []
        
        # Add company name
        if business_info.get('company_name'):
            summary_parts.append(business_info['company_name'])
        
        # Add key description
        for field in ['meta_description', 'business_description', 'about_text']:
            if business_info.get(field) and len(business_info[field]) > 20:
                summary_parts.append(business_info[field][:200])
                break
        
        # Add services if available
        if business_info.get('services_text') and len(business_info['services_text']) > 20:
            summary_parts.append(f"Services: {business_info['services_text'][:150]}")
        
        return ' | '.join(summary_parts)
    
    def _find_best_email(self, emails: list) -> str:
        """Find the best email from a list."""
        # Implementation from parent class
        for email in emails:
            if email and '@' in email:
                clean_email = email.replace('mailto:', '').strip()
                if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', clean_email):
                    return clean_email
        return ''
    
    def _find_best_phone(self, phones: list) -> str:
        """Find the best phone from a list."""
        # Implementation from parent class
        for phone in phones:
            if phone:
                clean_phone = phone.replace('tel:', '').strip()
                if len(clean_phone) >= 10:
                    return clean_phone
        return ''
    
    def _find_best_social(self, social_links: list) -> dict:
        """Find the best social media link."""
        # Implementation from parent class
        for link in social_links:
            if link and any(platform in link.lower() for platform in ['facebook', 'instagram', 'twitter', 'linkedin']):
                for platform in ['facebook', 'instagram', 'twitter', 'linkedin']:
                    if platform in link.lower():
                        return {
                            'platform': platform,
                            'url': link,
                            'confidence': 0.8
                        }
        return {}
    
    async def extract_enhanced_business_info(self, urls: list) -> list:
        """Extract enhanced business information from URLs."""
        print(f"🏢 Starting enhanced business extraction for {len(urls)} URLs...")
        
        results = []
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            print(f"📊 Processing batch {i//self.batch_size + 1}: {len(batch)} URLs")
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def process_url(url):
                async with semaphore:
                    from crawl4ai import AsyncWebCrawler
                    async with AsyncWebCrawler() as crawler:
                        return await self._extract_from_page_enhanced(crawler, url)
            
            # Process batch concurrently
            batch_results = await asyncio.gather(*[process_url(url) for url in batch])
            results.extend(batch_results)
            
            # Progress update
            successful = len([r for r in batch_results if 'error' not in r])
            print(f"✅ Batch complete: {successful}/{len(batch)} successful")
        
        return results
    
    def export_enhanced_to_csv(self, results: list, filename: str):
        """Export enhanced results to CSV."""
        if not results:
            print("❌ No results to export")
            return
        
        # Flatten the results for CSV
        flattened = []
        for result in results:
            if 'error' not in result:
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    'email': result.get('email', ''),
                    'email_confidence': result.get('email_confidence', ''),
                    'phone': result.get('phone', ''),
                    'phone_confidence': result.get('phone_confidence', ''),
                    'social_platform': result.get('social_media', {}).get('platform', ''),
                    'social_url': result.get('social_media', {}).get('url', ''),
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'mission_vision_text': result.get('mission_vision_text', ''),
                    'business_description': result.get('business_description', ''),
                    'industry_keywords': result.get('industry_keywords', ''),
                    'location_text': result.get('location_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })
        
        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Enhanced results exported to: {filename}")


async def test_enhanced_extractor():
    """Test the enhanced business extractor."""
    print("🏢 TESTING ENHANCED BUSINESS EXTRACTOR")
    print("=" * 50)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:10]  # Test with 10 URLs
        print(f"✅ Testing with {len(urls)} URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    # Create enhanced extractor
    extractor = EnhancedBusinessExtractor(batch_size=10, max_concurrent=5)
    
    start_time = datetime.now()
    
    # Extract enhanced business information
    results = await extractor.extract_enhanced_business_info(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Show results
    successful = len([r for r in results if 'error' not in r])
    print(f"\n📊 ENHANCED EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"✅ Success: {successful}/{len(results)}")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    
    # Show sample results
    print(f"\n📋 SAMPLE ENHANCED DATA:")
    for i, result in enumerate([r for r in results if 'error' not in r][:3], 1):
        print(f"\n{i}. {result.get('url', 'Unknown URL')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print(f"   📧 Email: {result.get('email', 'N/A')}")
        print(f"   📄 Title: {result.get('page_title', 'N/A')[:80]}...")
        print(f"   📝 About: {result.get('about_text', 'N/A')[:100]}...")
        print(f"   🛠️  Services: {result.get('services_text', 'N/A')[:100]}...")
        print(f"   📍 Location: {result.get('location_text', 'N/A')[:50]}...")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"enhanced_business_extraction_{timestamp}.csv"
    extractor.export_enhanced_to_csv(results, filename)
    
    print(f"\n🎉 Enhanced extraction complete!")
    print(f"💾 Results saved to: {filename}")


if __name__ == "__main__":
    asyncio.run(test_enhanced_extractor())
