"""
Simple test script for practice URLs
Tests the web scraper with the practice dataset to verify everything works.
"""

import asyncio
import pandas as pd
import sys
import os
from datetime import datetime

# Add the aganl directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


async def test_small_batch():
    """Test with a very small batch first."""
    print("🧪 TESTING SMALL BATCH (5 URLs)")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:5]  # Just first 5 URLs
        print(f"✅ Loaded {len(urls)} test URLs:")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    # Create extractor with very conservative settings
    extractor = PerfectContactExtractor(batch_size=2, max_concurrent=1)
    
    print(f"\n🚀 Starting extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        
        # Show detailed results
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result.get('url', 'Unknown URL')}")
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                continue
            
            email = result.get('email')
            phone = result.get('phone')
            social = result.get('social_media')
            
            if email:
                print(f"   📧 Email: {email.get('email', 'N/A')} (confidence: {email.get('confidence', 0):.1%})")
            else:
                print(f"   📧 Email: Not found")
            
            if phone:
                print(f"   📞 Phone: {phone.get('phone', 'N/A')} (confidence: {phone.get('confidence', 0):.1%})")
            else:
                print(f"   📞 Phone: Not found")
            
            if social:
                print(f"   🌐 Social: {social.get('platform', 'N/A')} - {social.get('handle', 'N/A')}")
                print(f"       URL: {social.get('url', 'N/A')}")
            else:
                print(f"   🌐 Social: Not found")
            
            print(f"   📊 Pages checked: {result.get('pages_checked', 0)}/{result.get('pages_available', 0)}")
        
        # Print summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"practice_test_small_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_medium_batch():
    """Test with a medium batch if small batch works."""
    print("\n🧪 TESTING MEDIUM BATCH (15 URLs)")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:15]  # First 15 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # Create extractor with moderate settings
    extractor = PerfectContactExtractor(batch_size=5, max_concurrent=2)
    
    print(f"\n🚀 Starting extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 MEDIUM BATCH RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        
        # Print summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"practice_test_medium_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        # Show success rate
        successful = len([r for r in results if 'error' not in r])
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        print(f"\n🎯 SUCCESS METRICS:")
        print(f"   • Successful extractions: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "   • Emails found: 0/0")
        print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "   • Social media found: 0/0")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_full_practice_set():
    """Test with the full practice URL set."""
    print("\n🧪 TESTING FULL PRACTICE SET")
    print("=" * 40)
    
    # Load all practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()
        print(f"✅ Loaded {len(urls)} practice URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # Create extractor with production-like settings
    extractor = PerfectContactExtractor(batch_size=10, max_concurrent=3)
    
    print(f"\n🚀 Starting full practice extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 FULL PRACTICE RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds ({duration/60:.1f} minutes)")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        
        # Print detailed summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"practice_test_full_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        # Calculate projections for main dataset
        main_dataset_size = 8004  # From the main dataset
        projected_time = (main_dataset_size / len(urls)) * duration
        
        print(f"\n🔮 PROJECTIONS FOR MAIN DATASET ({main_dataset_size} URLs):")
        print(f"   • Estimated time: {projected_time/3600:.1f} hours")
        print(f"   • Recommended batch size: {extractor.batch_size}")
        print(f"   • Recommended concurrent: {extractor.max_concurrent}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🚀 PRACTICE URL TESTING SUITE")
    print("=" * 50)
    print("This will test the web scraper with practice URLs in stages:")
    print("1. Small batch (5 URLs) - Basic functionality test")
    print("2. Medium batch (15 URLs) - Performance test")
    print("3. Full practice set (~100 URLs) - Scale test")
    print("=" * 50)
    
    try:
        # Stage 1: Small batch test
        print("STAGE 1: Small batch test")
        success = await test_small_batch()
        
        if not success:
            print("❌ Small batch test failed. Stopping here.")
            return
        
        print("\n✅ Small batch test successful!")
        
        # Ask user if they want to continue
        print("\nPress Enter to continue to medium batch test, or Ctrl+C to stop...")
        input()
        
        # Stage 2: Medium batch test
        print("\nSTAGE 2: Medium batch test")
        success = await test_medium_batch()
        
        if not success:
            print("❌ Medium batch test failed. Stopping here.")
            return
        
        print("\n✅ Medium batch test successful!")
        
        # Ask user if they want to continue
        print("\nPress Enter to continue to full practice set test, or Ctrl+C to stop...")
        input()
        
        # Stage 3: Full practice set test
        print("\nSTAGE 3: Full practice set test")
        success = await test_full_practice_set()
        
        if success:
            print("\n🎉 ALL TESTS SUCCESSFUL!")
            print("The web scraper is working correctly and ready for the main dataset.")
            print("\nNext steps:")
            print("1. Review the generated CSV files")
            print("2. Run process_urls.py to process your main dataset")
            print("3. Start with a small limit and scale up")
        else:
            print("\n❌ Full practice set test failed.")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
