"""
Find Maximum Speed Script
Tests different concurrency levels to find the fastest settings for your system.
"""

import asyncio
import pandas as pd
import sys
import os
from datetime import datetime

from aganl.perfect_contact_extractor import PerfectContactExtractor


async def test_speed_config(batch_size, max_concurrent, num_urls=20):
    """Test a specific speed configuration."""
    try:
        # Load practice URLs
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:num_urls]
        
        # Create extractor with specified settings
        extractor = PerfectContactExtractor(
            batch_size=batch_size,
            max_concurrent=max_concurrent
        )
        
        print(f"🧪 Testing: batch_size={batch_size}, concurrent={max_concurrent}, urls={num_urls}")
        
        start_time = datetime.now()
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        rate = len(urls) / duration
        successful = len([r for r in results if 'error' not in r])
        success_rate = successful / len(results) * 100
        
        print(f"   ✅ Rate: {rate:.2f} URLs/sec, Success: {success_rate:.1f}%, Time: {duration:.1f}s")
        
        return {
            'batch_size': batch_size,
            'max_concurrent': max_concurrent,
            'rate': rate,
            'success_rate': success_rate,
            'duration': duration,
            'successful': successful,
            'total': len(results)
        }
        
    except Exception as e:
        print(f"   ❌ Failed: {str(e)[:100]}...")
        return None


async def find_optimal_speed():
    """Find the optimal speed settings for your system."""
    print("🔍 FINDING OPTIMAL SPEED SETTINGS")
    print("=" * 50)
    print("Testing different configurations to find your system's sweet spot...")
    
    # Test configurations (batch_size, max_concurrent)
    configs = [
        # Conservative
        (10, 5),
        (10, 10),
        
        # Moderate
        (20, 10),
        (20, 15),
        (20, 20),
        
        # Aggressive
        (30, 15),
        (30, 20),
        (30, 25),
        
        # Very Aggressive
        (40, 20),
        (40, 25),
        (40, 30),
        
        # Insane
        (50, 25),
        (50, 30),
        (50, 35),
    ]
    
    results = []
    
    for batch_size, max_concurrent in configs:
        result = await test_speed_config(batch_size, max_concurrent, num_urls=20)
        if result:
            results.append(result)
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    if not results:
        print("❌ No successful tests!")
        return
    
    # Find the best configuration
    print(f"\n📊 RESULTS SUMMARY:")
    print("=" * 70)
    print(f"{'Batch':<6} {'Concurrent':<10} {'Rate':<12} {'Success':<8} {'Time':<6}")
    print("-" * 70)
    
    for r in sorted(results, key=lambda x: x['rate'], reverse=True):
        print(f"{r['batch_size']:<6} {r['max_concurrent']:<10} {r['rate']:<12.2f} {r['success_rate']:<8.1f}% {r['duration']:<6.1f}s")
    
    # Find optimal settings
    best_rate = max(results, key=lambda x: x['rate'])
    best_success = max(results, key=lambda x: x['success_rate'])
    
    print(f"\n🏆 OPTIMAL SETTINGS:")
    print(f"   🚀 FASTEST: batch_size={best_rate['batch_size']}, concurrent={best_rate['max_concurrent']}")
    print(f"      Rate: {best_rate['rate']:.2f} URLs/sec, Success: {best_rate['success_rate']:.1f}%")
    
    if best_rate != best_success:
        print(f"   ✅ MOST RELIABLE: batch_size={best_success['batch_size']}, concurrent={best_success['max_concurrent']}")
        print(f"      Rate: {best_success['rate']:.2f} URLs/sec, Success: {best_success['success_rate']:.1f}%")
    
    # Calculate projections for main dataset
    main_dataset_size = 8004
    fastest_time = main_dataset_size / best_rate['rate']
    
    print(f"\n🎯 MAIN DATASET PROJECTIONS:")
    print(f"   • Using fastest settings: {fastest_time/60:.1f} minutes ({fastest_time/3600:.1f} hours)")
    print(f"   • That's {main_dataset_size} URLs in {fastest_time:.0f} seconds!")
    
    return best_rate


async def test_recommended_settings():
    """Test with the recommended high-speed settings."""
    print("\n🚀 TESTING RECOMMENDED HIGH-SPEED SETTINGS")
    print("=" * 50)
    
    # Recommended settings based on typical performance
    extractor = PerfectContactExtractor(
        batch_size=30,      # Good balance
        max_concurrent=20   # Aggressive but stable
    )
    
    # Load more URLs for a real test
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:50]  # Test with 50 URLs
        print(f"✅ Testing with {len(urls)} URLs")
        print(f"⚙️  Settings: batch_size=30, max_concurrent=20")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        rate = len(urls) / duration
        successful = len([r for r in results if 'error' not in r])
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        print(f"\n📊 RECOMMENDED SETTINGS RESULTS:")
        print(f"   ⏱️  Time: {duration:.1f} seconds")
        print(f"   ⚡ Rate: {rate:.2f} URLs/second")
        print(f"   ✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "   📧 Emails: 0/0")
        print(f"   🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "   🌐 Social: 0/0")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"recommended_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        print(f"   💾 Results saved to: {filename}")
        
        # Main dataset projection
        main_dataset_size = 8004
        projected_time = main_dataset_size / rate
        print(f"\n🎯 MAIN DATASET PROJECTION:")
        print(f"   • {main_dataset_size} URLs at {rate:.2f} URLs/sec")
        print(f"   • Total time: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        
        return rate
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def main():
    """Main function to find maximum speed."""
    print("🔥 MAXIMUM SPEED FINDER")
    print("=" * 60)
    print("This will:")
    print("1. Test different configurations to find optimal settings")
    print("2. Test recommended high-speed settings")
    print("3. Give you the best settings for your system")
    print("=" * 60)
    
    try:
        # Step 1: Find optimal settings
        optimal = await find_optimal_speed()
        
        print("\nPress Enter to test recommended settings...")
        input()
        
        # Step 2: Test recommended settings
        recommended_rate = await test_recommended_settings()
        
        print(f"\n🎉 SPEED TESTING COMPLETE!")
        print("=" * 50)
        
        if optimal:
            print(f"🏆 Your optimal settings: batch_size={optimal['batch_size']}, concurrent={optimal['max_concurrent']}")
            print(f"   Rate: {optimal['rate']:.2f} URLs/second")
        
        if recommended_rate > 0:
            print(f"🚀 Recommended settings rate: {recommended_rate:.2f} URLs/second")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"   1. Use these settings in your main processing script")
        print(f"   2. Start with a small batch of your real data (20-50 URLs)")
        print(f"   3. Scale up to the full dataset once you're confident")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Speed testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
