"""
Optimized Contact + Business Extractor
Maximum contact extraction success with clean business intelligence.
"""

import asyncio
import pandas as pd
import re
import json
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor
from crawl4ai import CrawlerRunConfig, JsonCssExtractionStrategy


class OptimizedContactBusinessExtractor(PerfectContactExtractor):
    """Optimized extractor for maximum contact success + business intelligence."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings."""
        super().__init__(batch_size, max_concurrent)
    
    async def _extract_from_page_optimized(self, crawler, url: str) -> dict:
        """Optimized extraction with clean data formatting."""
        try:
            # STEP 1: Use original perfect contact extraction
            contact_result = await self._extract_from_page_perfect(crawler, url)
            
            # STEP 2: Extract business info from the same page content
            business_result = await self._extract_business_info_optimized(crawler, url)
            
            # STEP 3: Clean and format the data properly
            result = {
                "url": url,
                "timestamp": datetime.now().isoformat(),
            }
            
            # Clean contact data (extract values from dictionaries)
            result['email'] = self._extract_clean_value(contact_result.get('email'))
            result['email_confidence'] = self._extract_confidence(contact_result.get('email'))
            result['phone'] = self._extract_clean_value(contact_result.get('phone'))
            result['phone_confidence'] = self._extract_confidence(contact_result.get('phone'))
            result['social_media'] = self._extract_clean_social(contact_result.get('social_media'))
            result['pages_checked'] = contact_result.get('pages_checked', 0)
            
            # Add business data
            result.update(business_result)
            
            return result
            
        except Exception as e:
            print(f"❌ Optimized extraction failed for {url}: {str(e)}")
            return {"url": url, "error": f"Extraction failed: {str(e)}"}
    
    def _extract_clean_value(self, data):
        """Extract clean value from contact data."""
        if isinstance(data, dict):
            return data.get('email') or data.get('phone') or data.get('url', '')
        return data or ''
    
    def _extract_confidence(self, data):
        """Extract confidence score from contact data."""
        if isinstance(data, dict):
            return data.get('confidence', 0)
        return 0.8 if data else 0
    
    def _extract_clean_social(self, data):
        """Extract clean social media info."""
        if isinstance(data, dict):
            platform = data.get('platform', '')
            url = data.get('url', '')
            if platform and url:
                return f"{platform}: {url}"
            return url
        return data or ''
    
    async def _extract_business_info_optimized(self, crawler, url: str) -> dict:
        """Extract business info with better patterns."""
        try:
            # Get the page content that was already loaded
            config = CrawlerRunConfig()
            result = await crawler.arun(url=url, config=config)
            
            if result.success and result.cleaned_html:
                content = result.cleaned_html
                
                # Extract business info using regex patterns
                business_info = {}
                
                # Page title
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
                business_info['page_title'] = title_match.group(1).strip() if title_match else ''
                
                # Meta description
                meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', content, re.IGNORECASE)
                business_info['meta_description'] = meta_match.group(1).strip() if meta_match else ''
                
                # Company name (from h1, title, or common patterns)
                company_patterns = [
                    r'<h1[^>]*>([^<]+)</h1>',
                    r'<title[^>]*>([^<|]+)',
                    r'class=["\'][^"\']*company[^"\']*["\'][^>]*>([^<]+)',
                    r'class=["\'][^"\']*brand[^"\']*["\'][^>]*>([^<]+)',
                ]
                
                company_name = ''
                for pattern in company_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        candidate = match.group(1).strip()
                        if len(candidate) > 2 and len(candidate) < 100:
                            company_name = candidate
                            break
                
                business_info['company_name'] = company_name
                
                # About text (look for about sections)
                about_patterns = [
                    r'<[^>]*class=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
                    r'<[^>]*id=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
                    r'(?i)about\s+us[^<]*<[^>]*>([^<]+)',
                    r'(?i)our\s+story[^<]*<[^>]*>([^<]+)',
                ]
                
                about_texts = []
                for pattern in about_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    about_texts.extend([m.strip() for m in matches if len(m.strip()) > 20])
                
                business_info['about_text'] = ' '.join(about_texts[:3])[:500] if about_texts else ''
                
                # Services text
                service_patterns = [
                    r'<[^>]*class=["\'][^"\']*service[^"\']*["\'][^>]*>([^<]+)',
                    r'(?i)services[^<]*<[^>]*>([^<]+)',
                    r'(?i)what\s+we\s+do[^<]*<[^>]*>([^<]+)',
                ]
                
                service_texts = []
                for pattern in service_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    service_texts.extend([m.strip() for m in matches if len(m.strip()) > 10])
                
                business_info['services_text'] = ' '.join(service_texts[:3])[:400] if service_texts else ''
                
                # Key headings (h1, h2, h3)
                heading_matches = re.findall(r'<h[1-3][^>]*>([^<]+)</h[1-3]>', content, re.IGNORECASE)
                headings = [h.strip() for h in heading_matches if len(h.strip()) > 2 and len(h.strip()) < 100]
                business_info['key_headings'] = ' | '.join(headings[:6])
                
                # Generate business summary
                summary_parts = []
                if business_info['company_name']:
                    summary_parts.append(business_info['company_name'])
                if business_info['meta_description']:
                    summary_parts.append(business_info['meta_description'][:100])
                elif business_info['about_text']:
                    summary_parts.append(business_info['about_text'][:100])
                
                business_info['business_summary'] = ' | '.join(summary_parts)
                
                return business_info
                
        except Exception as e:
            print(f"⚠️  Business info extraction failed for {url}: {str(e)}")
        
        return {
            'page_title': '',
            'meta_description': '',
            'company_name': '',
            'about_text': '',
            'services_text': '',
            'key_headings': '',
            'business_summary': ''
        }
    
    async def extract_optimized_data(self, urls: list) -> list:
        """Extract optimized contact + business data."""
        print(f"🔥 Starting OPTIMIZED extraction for {len(urls)} URLs...")
        print(f"   📞 PRIORITY: Maximum contact extraction success")
        print(f"   🏢 BONUS: Clean business intelligence")
        
        results = []
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            print(f"📊 Processing batch {i//self.batch_size + 1}: {len(batch)} URLs")
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def process_url(url):
                async with semaphore:
                    from crawl4ai import AsyncWebCrawler
                    async with AsyncWebCrawler() as crawler:
                        return await self._extract_from_page_optimized(crawler, url)
            
            # Process batch concurrently
            batch_results = await asyncio.gather(*[process_url(url) for url in batch])
            results.extend(batch_results)
            
            # Progress update
            successful = len([r for r in batch_results if 'error' not in r])
            print(f"✅ Batch complete: {successful}/{len(batch)} successful")
        
        return results
    
    def export_optimized_to_csv(self, results: list, filename: str):
        """Export optimized results to CSV with clean formatting."""
        if not results:
            print("❌ No results to export")
            return
        
        # Flatten the results for CSV
        flattened = []
        for result in results:
            if 'error' not in result:
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    # CONTACT INFO (clean values)
                    'email': result.get('email', ''),
                    'email_confidence': result.get('email_confidence', ''),
                    'phone': result.get('phone', ''),
                    'phone_confidence': result.get('phone_confidence', ''),
                    'social_media': result.get('social_media', ''),
                    'pages_checked': result.get('pages_checked', ''),
                    # BUSINESS INFO
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })
        
        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Optimized results exported to: {filename}")


async def test_optimized_extractor():
    """Test the optimized extractor on first 20 prospects."""
    print("🚀 TESTING OPTIMIZED CONTACT + BUSINESS EXTRACTOR")
    print("=" * 60)
    print("Maximum contact extraction with clean business intelligence!")
    
    # Load first 20 prospects for testing
    try:
        df = pd.read_csv('first_100_prospects_20250915_002842.csv')
        urls = df['website'].dropna().tolist()[:20]  # Test with 20 URLs
        print(f"✅ Testing with {len(urls)} prospect URLs")
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return
    
    # Create optimized extractor
    extractor = OptimizedContactBusinessExtractor(batch_size=20, max_concurrent=30)
    
    print(f"🚀 OPTIMIZED EXTRACTOR SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Strategy: Maximum contact success + clean business data")
    
    start_time = datetime.now()
    
    # Extract optimized data
    results = await extractor.extract_optimized_data(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Analyze results
    successful = len([r for r in results if 'error' not in r])
    
    print(f"\n📊 OPTIMIZED EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    
    # Analyze contact extraction success
    if successful > 0:
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        company_names = len([r for r in results if 'error' not in r and r.get('company_name')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        
        print(f"\n🎯 CONTACT EXTRACTION SUCCESS:")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        
        print(f"\n🏢 BUSINESS INFO SUCCESS:")
        print(f"   🏢 Company names: {company_names}/{successful} ({company_names/successful*100:.1f}%)")
        print(f"   📝 About sections: {about_found}/{successful} ({about_found/successful*100:.1f}%)")
    
    # Show sample results with clean formatting
    print(f"\n📋 SAMPLE OPTIMIZED RESULTS:")
    print("=" * 80)
    
    for i, result in enumerate([r for r in results if 'error' not in r][:5], 1):
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        print(f"   📧 Email: {result.get('email', 'N/A')} (confidence: {result.get('email_confidence', 'N/A')})")
        print(f"   📞 Phone: {result.get('phone', 'N/A')} (confidence: {result.get('phone_confidence', 'N/A')})")
        print(f"   🌐 Social: {result.get('social_media', 'N/A')}")
        print(f"   📄 Pages checked: {result.get('pages_checked', 'N/A')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print(f"   📄 Title: {result.get('page_title', 'N/A')[:60]}...")
        print(f"   📝 About: {result.get('about_text', 'N/A')[:80]}...")
        print(f"   🛠️  Services: {result.get('services_text', 'N/A')[:80]}...")
        print("-" * 80)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"optimized_extraction_test_{timestamp}.csv"
    extractor.export_optimized_to_csv(results, filename)
    
    print(f"\n🎉 OPTIMIZED EXTRACTION TEST COMPLETE!")
    print(f"💾 Results saved to: {filename}")
    
    print(f"\n💡 TARGET ACHIEVEMENTS:")
    print(f"   📧 Email extraction: {emails_found/successful*100:.1f}% (target: 60-80%)")
    print(f"   📞 Phone extraction: {phones_found/successful*100:.1f}% (target: 70-90%)")
    print(f"   🏢 Clean data formatting: ✅")
    print(f"   🚀 Business intelligence: ✅")


if __name__ == "__main__":
    asyncio.run(test_optimized_extractor())
