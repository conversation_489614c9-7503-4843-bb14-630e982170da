"""
Overnight Pittsburgh Prospects Extraction
Simple script to run the full dataset extraction overnight.
"""

import asyncio
from full_dataset_extractor import FullDatasetExtractor
from datetime import datetime

async def main():
    """Run the overnight extraction."""
    print("🌙 OVERNIGHT PITTSBURGH PROSPECTS EXTRACTION")
    print("=" * 60)
    print("This will process all 8,004 prospects overnight.")
    print("You can safely leave your PC - it will run automatically!")
    print("Expected completion time: 3-4 hours")
    
    # Configuration
    INPUT_FILE = "pittsburgh_coffee_prospects_FINAL_20250914_164553.csv"
    OUTPUT_FILE = f"pittsburgh_prospects_with_contacts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    print(f"\n📁 Input: {INPUT_FILE}")
    print(f"📁 Output: {OUTPUT_FILE}")
    
    # Create extractor with optimized overnight settings
    extractor = FullDatasetExtractor(batch_size=20, max_concurrent=30)
    
    # Process the full dataset
    await extractor.process_full_dataset(INPUT_FILE, OUTPUT_FILE)
    
    print("\n🎉 EXTRACTION COMPLETE! Check your output file.")
    print("💤 You can now wake up to a complete lead database!")

if __name__ == "__main__":
    asyncio.run(main())
