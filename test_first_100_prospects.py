"""
Test Enhanced Business Extractor with First 100 Pittsburgh Coffee Prospects
"""

import asyncio
import pandas as pd
from datetime import datetime
from enhanced_business_extractor import EnhancedBusinessExtractor


def create_first_100_dataset():
    """Extract first 100 URLs from main dataset and create test dataset."""
    print("📊 CREATING FIRST 100 PROSPECTS DATASET")
    print("=" * 50)
    
    try:
        # Load main dataset
        main_df = pd.read_csv('pittsburgh_coffee_prospects_FINAL_20250914_164553.csv')
        print(f"✅ Loaded main dataset: {len(main_df)} total prospects")
        
        # Filter out rows without websites
        prospects_with_websites = main_df[main_df['website'].notna() & (main_df['website'] != '')]
        print(f"✅ Found {len(prospects_with_websites)} prospects with websites")
        
        # Take first 100 with websites
        first_100 = prospects_with_websites.head(100).copy()
        print(f"✅ Selected first {len(first_100)} prospects for testing")
        
        # Save to new file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"first_100_prospects_{timestamp}.csv"
        first_100.to_csv(filename, index=False)
        print(f"💾 Saved test dataset: {filename}")
        
        # Show sample of what we're testing
        print(f"\n📋 SAMPLE PROSPECTS TO TEST:")
        for i, row in first_100.head(5).iterrows():
            print(f"   {i+1}. {row['name']} - {row['website']}")
        print(f"   ... and {len(first_100)-5} more")
        
        return filename, first_100
        
    except Exception as e:
        print(f"❌ Error creating test dataset: {e}")
        return None, None


async def test_first_100_prospects():
    """Test enhanced business extraction on first 100 prospects."""
    print("\n🏢 TESTING ENHANCED EXTRACTION ON FIRST 100 PROSPECTS")
    print("=" * 60)
    
    # Create test dataset
    dataset_filename, prospects_df = create_first_100_dataset()
    if dataset_filename is None:
        return
    
    # Extract URLs for testing
    urls = prospects_df['website'].dropna().tolist()
    print(f"\n🎯 Testing enhanced extraction on {len(urls)} prospect websites")
    
    # Create enhanced extractor with optimized settings
    extractor = EnhancedBusinessExtractor(
        batch_size=20,      # Optimized from speed tests
        max_concurrent=30   # Optimized from speed tests
    )
    
    print(f"🚀 OPTIMIZED SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • These settings gave us 2.21 URLs/second in testing!")
    
    start_time = datetime.now()
    
    # Extract enhanced business information
    print(f"\n🚀 Starting enhanced extraction...")
    results = await extractor.extract_enhanced_business_info(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Analyze results
    successful = len([r for r in results if 'error' not in r])
    failed = len(results) - successful
    
    print(f"\n📊 EXTRACTION RESULTS:")
    print(f"⏱️  Total time: {duration:.1f} seconds ({duration/60:.1f} minutes)")
    print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Successful: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    print(f"❌ Failed: {failed}/{len(results)} ({failed/len(results)*100:.1f}%)")
    
    # Show projection for full dataset
    if successful > 0:
        success_rate = successful / len(results)
        full_dataset_size = 8004
        projected_time = full_dataset_size / (len(urls)/duration)
        
        print(f"\n🔮 FULL DATASET PROJECTION:")
        print(f"   • {full_dataset_size} total prospects")
        print(f"   • At {len(urls)/duration:.2f} URLs/sec = {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        print(f"   • Expected successful extractions: {int(full_dataset_size * success_rate)}")
    
    # Analyze what business information we captured
    if successful > 0:
        print(f"\n📈 BUSINESS INTELLIGENCE CAPTURED:")
        
        # Count different types of information found
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media', {}).get('platform')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        services_found = len([r for r in results if 'error' not in r and r.get('services_text')])
        mission_found = len([r for r in results if 'error' not in r and r.get('mission_vision_text')])
        location_found = len([r for r in results if 'error' not in r and r.get('location_text')])
        company_names = len([r for r in results if 'error' not in r and r.get('company_name')])
        
        print(f"   📧 Emails extracted: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones extracted: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social media: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        print(f"   🏢 Company names: {company_names}/{successful} ({company_names/successful*100:.1f}%)")
        print(f"   📝 About sections: {about_found}/{successful} ({about_found/successful*100:.1f}%)")
        print(f"   🛠️  Services info: {services_found}/{successful} ({services_found/successful*100:.1f}%)")
        print(f"   🎯 Mission/Vision: {mission_found}/{successful} ({mission_found/successful*100:.1f}%)")
        print(f"   📍 Location info: {location_found}/{successful} ({location_found/successful*100:.1f}%)")
    
    # Show sample enhanced results
    print(f"\n📋 SAMPLE ENHANCED BUSINESS DATA:")
    print("=" * 80)
    
    successful_results = [r for r in results if 'error' not in r]
    for i, result in enumerate(successful_results[:5], 1):
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        
        # Find corresponding prospect info
        prospect_info = prospects_df[prospects_df['website'] == result.get('url')]
        if not prospect_info.empty:
            prospect = prospect_info.iloc[0]
            print(f"   📋 Original Data: {prospect['name']} | {prospect['primary_type']} | {prospect.get('phone', 'No phone')}")
        
        print(f"   📧 Email: {result.get('email', 'N/A')}")
        print(f"   📞 Phone: {result.get('phone', 'N/A')}")
        print(f"   🌐 Social: {result.get('social_media', {}).get('platform', 'N/A')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print(f"   📄 Title: {result.get('page_title', 'N/A')[:60]}...")
        print(f"   📝 About: {result.get('about_text', 'N/A')[:100]}...")
        print(f"   🛠️  Services: {result.get('services_text', 'N/A')[:100]}...")
        print(f"   📋 Summary: {result.get('business_summary', 'N/A')[:150]}...")
        print("-" * 80)
    
    # Show some failures for debugging
    failures = [r for r in results if 'error' in r]
    if failures:
        print(f"\n🔍 SAMPLE FAILURES (for debugging):")
        for i, failure in enumerate(failures[:3], 1):
            print(f"   {i}. {failure.get('url', 'Unknown')}: {failure.get('error', 'Unknown error')}")
    
    # Export enhanced results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_filename = f"first_100_prospects_enhanced_{timestamp}.csv"
    extractor.export_enhanced_to_csv(results, results_filename)
    
    print(f"\n🎉 ENHANCED EXTRACTION COMPLETE!")
    print(f"💾 Original prospects: {dataset_filename}")
    print(f"💾 Enhanced results: {results_filename}")
    
    # Merge results back with original data
    print(f"\n🔗 MERGING ENHANCED DATA WITH ORIGINAL PROSPECTS...")
    merged_filename = merge_enhanced_with_original(prospects_df, results, timestamp)
    
    print(f"\n💡 NEXT STEPS:")
    print(f"   1. Review the enhanced data in: {merged_filename}")
    print(f"   2. Check success rate and data quality")
    print(f"   3. If satisfied, scale up to full {full_dataset_size} prospects")
    print(f"   4. Use enhanced data for better lead qualification!")
    
    return results_filename, merged_filename


def merge_enhanced_with_original(prospects_df, extraction_results, timestamp):
    """Merge enhanced extraction results with original prospect data."""
    
    # Create a mapping of URL to enhanced data
    enhanced_data = {}
    for result in extraction_results:
        if 'error' not in result:
            url = result.get('url')
            if url:
                enhanced_data[url] = result
    
    # Add enhanced columns to prospects dataframe
    enhanced_columns = [
        'extracted_email', 'extracted_phone', 'social_platform', 'social_url',
        'page_title', 'meta_description', 'company_name_extracted', 'about_text',
        'services_text', 'mission_vision_text', 'business_description',
        'industry_keywords', 'location_text', 'key_headings', 'business_summary'
    ]
    
    # Initialize new columns
    for col in enhanced_columns:
        prospects_df[col] = ''
    
    # Fill in enhanced data
    for idx, row in prospects_df.iterrows():
        website = row['website']
        if website in enhanced_data:
            enhanced = enhanced_data[website]
            prospects_df.at[idx, 'extracted_email'] = enhanced.get('email', '')
            prospects_df.at[idx, 'extracted_phone'] = enhanced.get('phone', '')
            prospects_df.at[idx, 'social_platform'] = enhanced.get('social_media', {}).get('platform', '')
            prospects_df.at[idx, 'social_url'] = enhanced.get('social_media', {}).get('url', '')
            prospects_df.at[idx, 'page_title'] = enhanced.get('page_title', '')
            prospects_df.at[idx, 'meta_description'] = enhanced.get('meta_description', '')
            prospects_df.at[idx, 'company_name_extracted'] = enhanced.get('company_name', '')
            prospects_df.at[idx, 'about_text'] = enhanced.get('about_text', '')
            prospects_df.at[idx, 'services_text'] = enhanced.get('services_text', '')
            prospects_df.at[idx, 'mission_vision_text'] = enhanced.get('mission_vision_text', '')
            prospects_df.at[idx, 'business_description'] = enhanced.get('business_description', '')
            prospects_df.at[idx, 'industry_keywords'] = enhanced.get('industry_keywords', '')
            prospects_df.at[idx, 'location_text'] = enhanced.get('location_text', '')
            prospects_df.at[idx, 'key_headings'] = enhanced.get('key_headings', '')
            prospects_df.at[idx, 'business_summary'] = enhanced.get('business_summary', '')
    
    # Save merged dataset
    merged_filename = f"first_100_prospects_merged_{timestamp}.csv"
    prospects_df.to_csv(merged_filename, index=False)
    print(f"✅ Merged dataset saved: {merged_filename}")
    
    return merged_filename


if __name__ == "__main__":
    asyncio.run(test_first_100_prospects())
