"""
Realistic Ultra Speed Test
Pushes speed without breaking everything.
"""

import asyncio
import pandas as pd
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


async def test_realistic_ultra_speed():
    """Test with realistic ultra-speed settings that actually work."""
    print("🚀 REALISTIC ULTRA SPEED TEST")
    print("=" * 50)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:40]  # Test with 40 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False, 0
    
    # REALISTIC ULTRA SETTINGS - Fast but stable
    extractor = PerfectContactExtractor(
        batch_size=25,      # Reasonable batch size
        max_concurrent=20   # High but not insane concurrency
    )
    
    print(f"🚀 REALISTIC ULTRA SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • This should be fast AND stable!")
    
    print(f"\n🚀 Starting realistic ultra speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        # PROPER SUCCESS COUNTING
        total_urls = len(results)
        successful = len([r for r in results if 'error' not in r])
        failed = total_urls - successful
        
        # Count actual content extraction
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        
        # Count pages checked
        total_pages_checked = sum(r.get('pages_checked', 0) for r in results if 'error' not in r)
        avg_pages_per_url = total_pages_checked / successful if successful > 0 else 0
        
        rate = len(urls) / duration
        success_rate = successful / total_urls * 100
        
        print(f"\n📊 REALISTIC ULTRA RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {rate:.2f} URLs/second")
        print(f"✅ REAL Success: {successful}/{total_urls} ({success_rate:.1f}%)")
        print(f"❌ REAL Failures: {failed}/{total_urls} ({(failed/total_urls)*100:.1f}%)")
        print(f"📧 Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "📧 Emails: 0/0")
        print(f"📞 Phones found: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)" if successful > 0 else "📞 Phones: 0/0")
        print(f"🌐 Social found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "🌐 Social: 0/0")
        print(f"📄 Avg pages checked: {avg_pages_per_url:.1f}")
        
        # Show some failures for debugging
        failures = [r for r in results if 'error' in r]
        if failures:
            print(f"\n🔍 SAMPLE FAILURES:")
            for i, failure in enumerate(failures[:3], 1):
                print(f"   {i}. {failure.get('url', 'Unknown')}: {failure.get('error', 'Unknown error')}")
        
        # Calculate projections for your main dataset
        if success_rate > 80:  # Only project if we have good success rate
            main_dataset_size = 8004
            projected_time = main_dataset_size / rate
            
            print(f"\n🔮 MAIN DATASET PROJECTION:")
            print(f"   • {main_dataset_size} URLs at {rate:.2f} URLs/sec")
            print(f"   • Total time: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
            print(f"   • Expected success: {success_rate:.1f}%")
        else:
            print(f"\n⚠️  Success rate too low ({success_rate:.1f}%) for reliable projections")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"realistic_ultra_speed_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        print(f"💾 Results saved to: {filename}")
        
        return True, rate, success_rate
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


async def test_different_concurrency_levels():
    """Test different concurrency levels to find the sweet spot."""
    print("\n🧪 TESTING DIFFERENT CONCURRENCY LEVELS")
    print("=" * 60)
    
    # Load URLs for testing
    try:
        df = pd.read_csv('practice_urls.csv')
        test_urls = df['URL'].dropna().tolist()[:20]  # Use 20 URLs for quick tests
        print(f"✅ Testing with {len(test_urls)} URLs each")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    # Test different concurrency levels
    configs = [
        (20, 15),   # Conservative
        (20, 20),   # Moderate
        (25, 25),   # Aggressive
        (30, 30),   # Very aggressive
        (25, 35),   # Push it
    ]
    
    results = []
    
    for batch_size, concurrent in configs:
        print(f"\n🧪 Testing: batch_size={batch_size}, concurrent={concurrent}")
        
        extractor = PerfectContactExtractor(
            batch_size=batch_size,
            max_concurrent=concurrent
        )
        
        start_time = datetime.now()
        
        try:
            extraction_results = await extractor.extract_perfect(test_urls)
            duration = (datetime.now() - start_time).total_seconds()
            
            # PROPER counting
            total = len(extraction_results)
            successful = len([r for r in extraction_results if 'error' not in r])
            failed = total - successful
            
            rate = len(test_urls) / duration
            success_rate = successful / total * 100
            
            print(f"   ✅ Rate: {rate:.2f} URLs/sec")
            print(f"   ✅ Success: {successful}/{total} ({success_rate:.1f}%)")
            print(f"   ❌ Failures: {failed}/{total} ({(failed/total)*100:.1f}%)")
            print(f"   ⏱️  Time: {duration:.1f}s")
            
            results.append({
                'batch_size': batch_size,
                'concurrent': concurrent,
                'rate': rate,
                'success_rate': success_rate,
                'duration': duration,
                'successful': successful,
                'failed': failed
            })
            
        except Exception as e:
            print(f"   ❌ FAILED: {str(e)[:50]}...")
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    if results:
        print(f"\n📊 CONCURRENCY TEST RESULTS:")
        print("=" * 80)
        print(f"{'Batch':<6} {'Concurrent':<10} {'Rate':<8} {'Success':<8} {'Failures':<8} {'Time':<6}")
        print("-" * 80)
        
        for r in sorted(results, key=lambda x: x['rate'], reverse=True):
            print(f"{r['batch_size']:<6} {r['concurrent']:<10} {r['rate']:<8.2f} {r['success_rate']:<8.1f}% {(r['failed']/20)*100:<8.1f}% {r['duration']:<6.1f}s")
        
        # Find best configuration (balance of speed and reliability)
        reliable_results = [r for r in results if r['success_rate'] >= 90]
        if reliable_results:
            best = max(reliable_results, key=lambda x: x['rate'])
            print(f"\n🏆 BEST RELIABLE CONFIGURATION:")
            print(f"   batch_size={best['batch_size']}, concurrent={best['concurrent']}")
            print(f"   Rate: {best['rate']:.2f} URLs/second")
            print(f"   Success: {best['success_rate']:.1f}%")
            return best
        else:
            print(f"\n⚠️  No configuration achieved 90%+ success rate!")
            best = max(results, key=lambda x: x['success_rate'])
            print(f"   Best success rate: {best['success_rate']:.1f}% at batch_size={best['batch_size']}, concurrent={best['concurrent']}")
            return best
    
    return None


async def main():
    """Main function."""
    print("🚀 REALISTIC ULTRA SPEED TESTING")
    print("=" * 60)
    print("This will test aggressive but stable settings.")
    print("No more browser crashes or false success reports!")
    print("=" * 60)
    
    try:
        # Test 1: Realistic ultra speed
        print("TEST 1: Realistic ultra speed")
        success, rate, success_rate = await test_realistic_ultra_speed()
        
        if success and success_rate > 80:
            print(f"\n✅ Realistic ultra speed successful!")
            print(f"   Rate: {rate:.2f} URLs/sec")
            print(f"   Success: {success_rate:.1f}%")
            
            # Ask if user wants to test different configurations
            print("\nWant to test different concurrency levels to optimize further? (y/n)")
            response = input().lower().strip()
            
            if response == 'y':
                best_config = await test_different_concurrency_levels()
                
                if best_config:
                    print(f"\n🎯 FINAL RECOMMENDATION:")
                    print(f"   Use: batch_size={best_config['batch_size']}, max_concurrent={best_config['concurrent']}")
                    print(f"   Expected rate: {best_config['rate']:.2f} URLs/second")
                    print(f"   Expected success: {best_config['success_rate']:.1f}%")
                    
                    # Final projection
                    main_dataset_size = 8004
                    time_needed = main_dataset_size / best_config['rate']
                    print(f"\n🔮 YOUR MAIN DATASET ({main_dataset_size} URLs):")
                    print(f"   • Time needed: {time_needed/60:.1f} minutes ({time_needed/3600:.1f} hours)")
                    print(f"   • Expected successful extractions: {(main_dataset_size * best_config['success_rate']/100):.0f}")
        else:
            print(f"\n⚠️  Need to reduce concurrency - too many failures!")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"   1. Use the recommended settings")
        print(f"   2. Test with 20-50 URLs from your real dataset")
        print(f"   3. Monitor for errors and adjust if needed")
        print(f"   4. Scale up gradually to full dataset")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
