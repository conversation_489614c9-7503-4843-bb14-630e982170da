"""
Final Contact + Business Extractor
Uses multi-page checking for maximum contact extraction success.
"""

import asyncio
import pandas as pd
import re
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


class FinalContactBusinessExtractor(PerfectContactExtractor):
    """Final extractor with multi-page checking and business intelligence."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings."""
        super().__init__(batch_size, max_concurrent)
    
    async def extract_final_data(self, urls: list) -> list:
        """Extract using the original perfect extraction + business enhancement."""
        print(f"🔥 Starting FINAL extraction for {len(urls)} URLs...")
        print(f"   📞 STRATEGY: Multi-page contact extraction + business intelligence")
        print(f"   📄 PAGES: Main → Contact → About (for maximum email success)")
        
        results = []
        
        # Use the original extract_perfect method (proven to work)
        original_results = await self.extract_perfect(urls)
        
        # Enhance each result with business intelligence
        print(f"🏢 Adding business intelligence to {len(original_results)} results...")
        
        for i, result in enumerate(original_results):
            if 'error' not in result:
                # Add business intelligence
                business_info = await self._extract_business_info_from_result(result)
                
                # Create enhanced result
                enhanced_result = {
                    **result,  # Keep all original contact data
                    **business_info  # Add business intelligence
                }
                
                results.append(enhanced_result)
            else:
                results.append(result)
            
            # Progress update
            if (i + 1) % 10 == 0:
                print(f"   ✅ Enhanced {i + 1}/{len(original_results)} results")
        
        return results
    
    async def _extract_business_info_from_result(self, result: dict) -> dict:
        """Extract business info from the URL in the result."""
        url = result.get('url', '')
        if not url:
            return self._empty_business_info()
        
        try:
            from crawl4ai import AsyncWebCrawler
            async with AsyncWebCrawler() as crawler:
                # Get page content
                from crawl4ai import CrawlerRunConfig
                config = CrawlerRunConfig()
                page_result = await crawler.arun(url=url, config=config)
                
                if page_result.success and page_result.cleaned_html:
                    return self._extract_business_from_html(page_result.cleaned_html, url)
                
        except Exception as e:
            print(f"⚠️  Business info extraction failed for {url}: {str(e)}")
        
        return self._empty_business_info()
    
    def _extract_business_from_html(self, html_content: str, url: str) -> dict:
        """Extract business information from HTML content."""
        business_info = {}
        
        # Page title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        business_info['page_title'] = title_match.group(1).strip() if title_match else ''
        
        # Meta description
        meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        business_info['meta_description'] = meta_match.group(1).strip() if meta_match else ''
        
        # Company name (multiple strategies)
        company_name = self._extract_company_name(html_content)
        business_info['company_name'] = company_name
        
        # About text
        about_text = self._extract_about_text(html_content)
        business_info['about_text'] = about_text
        
        # Services text
        services_text = self._extract_services_text(html_content)
        business_info['services_text'] = services_text
        
        # Key headings
        headings = self._extract_headings(html_content)
        business_info['key_headings'] = headings
        
        # Business summary
        business_info['business_summary'] = self._generate_business_summary(business_info)
        
        return business_info
    
    def _extract_company_name(self, html_content: str) -> str:
        """Extract company name using multiple strategies."""
        strategies = [
            # H1 tags
            r'<h1[^>]*>([^<]+)</h1>',
            # Title tag (before | or -)
            r'<title[^>]*>([^<|\\-]+)',
            # Company/brand class names
            r'class=["\'][^"\']*(?:company|brand|business)[^"\']*["\'][^>]*>([^<]+)',
            # Logo alt text
            r'<img[^>]*alt=["\']([^"\']*(?:logo|company|brand)[^"\']*)["\']',
            # Organization schema
            r'"@type":\s*"Organization"[^}]*"name":\s*"([^"]+)"',
        ]
        
        for pattern in strategies:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                candidate = match.strip()
                # Filter out common non-company names
                if (len(candidate) > 2 and len(candidate) < 100 and 
                    not any(word in candidate.lower() for word in ['home', 'welcome', 'contact', 'about', 'menu'])):
                    return candidate
        
        return ''
    
    def _extract_about_text(self, html_content: str) -> str:
        """Extract about/company description text."""
        patterns = [
            # About sections with class/id
            r'<[^>]*(?:class|id)=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
            # About us text
            r'(?i)about\s+us[^<]*<[^>]*>([^<]+)',
            # Company description
            r'(?i)(?:our\s+)?(?:company|story|mission)[^<]*<[^>]*>([^<]+)',
            # Meta description as fallback
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']',
        ]
        
        about_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 20 and len(text) < 500:
                    about_texts.append(text)
        
        return ' '.join(about_texts[:2])[:400] if about_texts else ''
    
    def _extract_services_text(self, html_content: str) -> str:
        """Extract services/products text."""
        patterns = [
            # Services sections
            r'<[^>]*(?:class|id)=["\'][^"\']*service[^"\']*["\'][^>]*>([^<]+)',
            # What we do
            r'(?i)what\s+we\s+do[^<]*<[^>]*>([^<]+)',
            # Services/products text
            r'(?i)(?:our\s+)?(?:services|products|offerings)[^<]*<[^>]*>([^<]+)',
        ]
        
        service_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 10 and len(text) < 300:
                    service_texts.append(text)
        
        return ' '.join(service_texts[:2])[:300] if service_texts else ''
    
    def _extract_headings(self, html_content: str) -> str:
        """Extract key headings."""
        heading_matches = re.findall(r'<h[1-3][^>]*>([^<]+)</h[1-3]>', html_content, re.IGNORECASE)
        headings = []
        for heading in heading_matches:
            clean_heading = re.sub(r'\s+', ' ', heading.strip())
            if len(clean_heading) > 2 and len(clean_heading) < 80:
                headings.append(clean_heading)
        
        return ' | '.join(headings[:6])
    
    def _generate_business_summary(self, business_info: dict) -> str:
        """Generate business summary."""
        parts = []
        
        if business_info.get('company_name'):
            parts.append(business_info['company_name'])
        
        if business_info.get('meta_description'):
            parts.append(business_info['meta_description'][:100])
        elif business_info.get('about_text'):
            parts.append(business_info['about_text'][:100])
        
        if business_info.get('services_text'):
            parts.append(f"Services: {business_info['services_text'][:80]}")
        
        return ' | '.join(parts)
    
    def _empty_business_info(self) -> dict:
        """Return empty business info structure."""
        return {
            'page_title': '',
            'meta_description': '',
            'company_name': '',
            'about_text': '',
            'services_text': '',
            'key_headings': '',
            'business_summary': ''
        }
    
    def export_final_to_csv(self, results: list, filename: str):
        """Export final results to CSV."""
        if not results:
            print("❌ No results to export")
            return
        
        # Flatten results for CSV
        flattened = []
        for result in results:
            if 'error' not in result:
                # Clean contact data
                email = result.get('email', '')
                if isinstance(email, dict):
                    email = email.get('email', '')
                
                phone = result.get('phone', '')
                if isinstance(phone, dict):
                    phone = phone.get('phone', '')
                
                social_media = result.get('social_media', '')
                if isinstance(social_media, dict):
                    platform = social_media.get('platform', '')
                    url = social_media.get('url', '')
                    social_media = f"{platform}: {url}" if platform and url else url
                
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    # CONTACT INFO (clean)
                    'email': email,
                    'email_confidence': result.get('email_confidence', ''),
                    'phone': phone,
                    'phone_confidence': result.get('phone_confidence', ''),
                    'social_media': social_media,
                    'pages_checked': result.get('pages_checked', ''),
                    # BUSINESS INFO
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })
        
        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Final results exported to: {filename}")


async def test_final_extractor():
    """Test the final extractor on first 20 prospects."""
    print("🚀 TESTING FINAL CONTACT + BUSINESS EXTRACTOR")
    print("=" * 60)
    print("Multi-page contact extraction + business intelligence!")
    
    # Load first 20 prospects
    try:
        df = pd.read_csv('first_100_prospects_20250915_002842.csv')
        urls = df['website'].dropna().tolist()[:20]  # Test with 20 URLs
        print(f"✅ Testing with {len(urls)} prospect URLs")
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return
    
    # Create final extractor
    extractor = FinalContactBusinessExtractor(batch_size=20, max_concurrent=30)
    
    print(f"🚀 FINAL EXTRACTOR SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Strategy: Multi-page contact extraction + business intelligence")
    
    start_time = datetime.now()
    
    # Extract final data
    results = await extractor.extract_final_data(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Analyze results
    successful = len([r for r in results if 'error' not in r])
    
    print(f"\n📊 FINAL EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    
    # Analyze contact extraction success
    if successful > 0:
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        company_names = len([r for r in results if 'error' not in r and r.get('company_name')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        pages_checked_avg = sum(r.get('pages_checked', 0) for r in results if 'error' not in r) / successful
        
        print(f"\n🎯 CONTACT EXTRACTION SUCCESS:")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        print(f"   📄 Avg pages checked: {pages_checked_avg:.1f}")
        
        print(f"\n🏢 BUSINESS INFO SUCCESS:")
        print(f"   🏢 Company names: {company_names}/{successful} ({company_names/successful*100:.1f}%)")
        print(f"   📝 About sections: {about_found}/{successful} ({about_found/successful*100:.1f}%)")
    
    # Show sample results
    print(f"\n📋 SAMPLE FINAL RESULTS:")
    print("=" * 80)
    
    for i, result in enumerate([r for r in results if 'error' not in r][:5], 1):
        email = result.get('email', '')
        if isinstance(email, dict):
            email = email.get('email', '')
        
        phone = result.get('phone', '')
        if isinstance(phone, dict):
            phone = phone.get('phone', '')
        
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        print(f"   📧 Email: {email} (confidence: {result.get('email_confidence', 'N/A')})")
        print(f"   📞 Phone: {phone} (confidence: {result.get('phone_confidence', 'N/A')})")
        print(f"   🌐 Social: {result.get('social_media', 'N/A')}")
        print(f"   📄 Pages checked: {result.get('pages_checked', 'N/A')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print(f"   📄 Title: {result.get('page_title', 'N/A')[:60]}...")
        print(f"   📝 About: {result.get('about_text', 'N/A')[:80]}...")
        print("-" * 80)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"final_extraction_test_{timestamp}.csv"
    extractor.export_final_to_csv(results, filename)
    
    print(f"\n🎉 FINAL EXTRACTION TEST COMPLETE!")
    print(f"💾 Results saved to: {filename}")
    
    print(f"\n💡 FINAL ACHIEVEMENTS:")
    print(f"   📧 Email extraction: {emails_found/successful*100:.1f}% (target: 60-80%)")
    print(f"   📞 Phone extraction: {phones_found/successful*100:.1f}% (target: 70-90%)")
    print(f"   📄 Multi-page checking: ✅ (avg {pages_checked_avg:.1f} pages)")
    print(f"   🏢 Business intelligence: ✅")
    print(f"   🚀 Ready for full dataset!")


if __name__ == "__main__":
    asyncio.run(test_final_extractor())
