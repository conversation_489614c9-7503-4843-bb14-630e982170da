"""
Lead Generation Contact Extraction Script
Processes URLs from datasets and extracts contact information using the web scraper.
"""

import asyncio
import pandas as pd
import csv
from datetime import datetime
from pathlib import Path
import sys
import os

# Add the aganl directory to the path so we can import the extractor
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


class LeadGenProcessor:
    """Processes lead generation datasets and extracts contact information."""
    
    def __init__(self, batch_size=10, max_concurrent=3):
        """Initialize the processor with conservative settings for testing."""
        self.extractor = PerfectContactExtractor(
            batch_size=batch_size, 
            max_concurrent=max_concurrent
        )
        self.results_cache = []
    
    def load_practice_urls(self, filename="practice_urls.csv"):
        """Load URLs from the practice dataset."""
        try:
            df = pd.read_csv(filename)
            urls = df['URL'].dropna().tolist()
            print(f"✅ Loaded {len(urls)} URLs from {filename}")
            return urls
        except Exception as e:
            print(f"❌ Error loading practice URLs: {e}")
            return []
    
    def load_main_dataset_urls(self, filename="pittsburgh_coffee_prospects_FINAL_20250914_164553.csv", limit=None):
        """Load URLs from the main dataset."""
        try:
            df = pd.read_csv(filename)
            # Extract URLs from the 'website' column
            urls = df['website'].dropna().tolist()
            
            if limit:
                urls = urls[:limit]
                print(f"✅ Loaded {len(urls)} URLs from {filename} (limited to {limit})")
            else:
                print(f"✅ Loaded {len(urls)} URLs from {filename}")
            
            return urls, df
        except Exception as e:
            print(f"❌ Error loading main dataset: {e}")
            return [], None
    
    async def process_urls(self, urls, description="URLs"):
        """Process a list of URLs and extract contact information."""
        if not urls:
            print("❌ No URLs to process")
            return []
        
        print(f"\n🚀 Processing {len(urls)} {description}...")
        print(f"⚙️  Settings: batch_size={self.extractor.batch_size}, max_concurrent={self.extractor.max_concurrent}")
        
        start_time = datetime.now()
        
        try:
            results = await self.extractor.extract_perfect(urls)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            # Print summary
            self.extractor.print_summary(results)
            print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
            print(f"⏱️  Total time: {duration:.1f} seconds")
            
            # Cache results for later use
            self.results_cache.extend(results)
            
            return results
            
        except Exception as e:
            print(f"❌ Error processing URLs: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def save_results(self, results, filename_prefix="contact_extraction"):
        """Save results to CSV file."""
        if not results:
            print("❌ No results to save")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.csv"
        
        self.extractor.export_to_csv(results, filename)
        print(f"💾 Results saved to: {filename}")
        return filename
    
    def merge_with_main_dataset(self, contact_results, main_dataset_file="pittsburgh_coffee_prospects_FINAL_20250914_164553.csv"):
        """Merge contact extraction results with the main dataset."""
        try:
            # Load the main dataset
            df_main = pd.read_csv(main_dataset_file)
            print(f"📊 Main dataset has {len(df_main)} rows")
            
            # Create a lookup dictionary from contact results
            contact_lookup = {}
            for result in contact_results:
                if 'error' not in result:
                    url = result.get('url', '')
                    email = result.get('email', {})
                    phone = result.get('phone', {})
                    social = result.get('social_media', {})
                    
                    contact_lookup[url] = {
                        'extracted_email': email.get('email', '') if email else '',
                        'extracted_email_confidence': email.get('confidence', '') if email else '',
                        'extracted_phone': phone.get('phone', '') if phone else '',
                        'extracted_phone_confidence': phone.get('confidence', '') if phone else '',
                        'extracted_social_platform': social.get('platform', '') if social else '',
                        'extracted_social_handle': social.get('handle', '') if social else '',
                        'extracted_social_url': social.get('url', '') if social else '',
                        'extraction_timestamp': result.get('timestamp', ''),
                        'pages_checked': result.get('pages_checked', 0),
                        'extraction_efficiency': result.get('efficiency', '')
                    }
            
            # Add new columns to the main dataset
            new_columns = ['extracted_email', 'extracted_email_confidence', 'extracted_phone', 
                          'extracted_phone_confidence', 'extracted_social_platform', 'extracted_social_handle',
                          'extracted_social_url', 'extraction_timestamp', 'pages_checked', 'extraction_efficiency']
            
            for col in new_columns:
                df_main[col] = ''
            
            # Merge the data
            matches = 0
            for idx, row in df_main.iterrows():
                website_url = row.get('website', '')
                if website_url in contact_lookup:
                    contact_data = contact_lookup[website_url]
                    for col, value in contact_data.items():
                        df_main.at[idx, col] = value
                    matches += 1
            
            print(f"✅ Merged contact data for {matches} URLs")
            
            # Save the updated dataset
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"pittsburgh_coffee_prospects_with_contacts_{timestamp}.csv"
            df_main.to_csv(output_filename, index=False)
            print(f"💾 Updated dataset saved to: {output_filename}")
            
            return output_filename, matches
            
        except Exception as e:
            print(f"❌ Error merging datasets: {e}")
            import traceback
            traceback.print_exc()
            return None, 0


async def test_practice_urls():
    """Test the system with practice URLs first."""
    print("🧪 TESTING WITH PRACTICE URLS")
    print("=" * 50)
    
    processor = LeadGenProcessor(batch_size=5, max_concurrent=2)  # Conservative settings for testing
    
    # Load and process practice URLs
    urls = processor.load_practice_urls()
    if not urls:
        return
    
    # Test with just the first 10 URLs
    test_urls = urls[:10]
    print(f"🎯 Testing with first {len(test_urls)} URLs")
    
    results = await processor.process_urls(test_urls, "practice URLs")
    
    if results:
        processor.save_results(results, "practice_test")
        
        # Show some sample results
        print(f"\n📋 SAMPLE RESULTS:")
        for i, result in enumerate(results[:3], 1):
            if 'error' not in result:
                email = result.get('email', {})
                social = result.get('social_media', {})
                print(f"{i}. {result['url']}")
                if email:
                    print(f"   📧 {email.get('email', 'N/A')}")
                if social:
                    print(f"   🌐 {social.get('platform', 'N/A')}: {social.get('handle', 'N/A')}")
                print(f"   📊 {result.get('pages_checked', 0)} pages checked")


async def process_main_dataset(limit=50):
    """Process URLs from the main dataset."""
    print(f"\n🏢 PROCESSING MAIN DATASET (Limited to {limit} URLs)")
    print("=" * 50)
    
    processor = LeadGenProcessor(batch_size=10, max_concurrent=3)
    
    # Load URLs from main dataset
    urls, df_main = processor.load_main_dataset_urls(limit=limit)
    if not urls:
        return
    
    # Process the URLs
    results = await processor.process_urls(urls, f"main dataset URLs (limited to {limit})")
    
    if results:
        # Save standalone results
        processor.save_results(results, "main_dataset_contacts")
        
        # Merge with main dataset
        output_file, matches = processor.merge_with_main_dataset(results)
        
        if output_file:
            print(f"\n🎉 SUCCESS! Created enhanced dataset: {output_file}")
            print(f"📊 Enhanced {matches} records with contact information")


async def main():
    """Main function to run the lead generation contact extraction."""
    try:
        print("🚀 LEAD GENERATION CONTACT EXTRACTION")
        print("=" * 60)
        print("This script will:")
        print("1. Test the web scraper with practice URLs")
        print("2. Process a limited set of URLs from your main dataset")
        print("3. Create an enhanced dataset with extracted contact info")
        print("=" * 60)
        
        # Step 1: Test with practice URLs
        await test_practice_urls()
        
        # Step 2: Process main dataset (limited)
        await process_main_dataset(limit=20)  # Start with just 20 URLs
        
        print(f"\n✅ PROCESSING COMPLETE!")
        print("Next steps:")
        print("- Review the results files")
        print("- If satisfied, increase the limit in process_main_dataset()")
        print("- Eventually process the full dataset")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Processing interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
