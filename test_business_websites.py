"""
Test Enhanced Business Extractor with Real Business Websites
"""

import asyncio
from enhanced_business_extractor import EnhancedBusinessExtractor
from datetime import datetime


async def test_real_business_websites():
    """Test with actual business websites to show enhanced extraction."""
    print("🏢 TESTING ENHANCED EXTRACTOR WITH REAL BUSINESS WEBSITES")
    print("=" * 60)
    
    # Real business websites that should have rich business information
    business_urls = [
        "https://www.starbucks.com",
        "https://www.nike.com", 
        "https://www.apple.com",
        "https://www.microsoft.com",
        "https://www.tesla.com",
        "https://www.airbnb.com",
        "https://www.uber.com",
        "https://www.spotify.com"
    ]
    
    print(f"✅ Testing with {len(business_urls)} business websites")
    print("These should have rich about sections, services, and business info!")
    
    # Create enhanced extractor with moderate settings
    extractor = EnhancedBusinessExtractor(batch_size=8, max_concurrent=4)
    
    start_time = datetime.now()
    
    # Extract enhanced business information
    results = await extractor.extract_enhanced_business_info(business_urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Show results
    successful = len([r for r in results if 'error' not in r])
    print(f"\n📊 BUSINESS WEBSITE EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"✅ Success: {successful}/{len(results)}")
    print(f"⚡ Rate: {len(business_urls)/duration:.2f} URLs/second")
    
    # Show detailed sample results
    print(f"\n📋 DETAILED BUSINESS INFORMATION EXTRACTED:")
    print("=" * 80)
    
    for i, result in enumerate([r for r in results if 'error' not in r][:4], 1):
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        print(f"   📧 Email: {result.get('email', 'N/A')}")
        print(f"   📞 Phone: {result.get('phone', 'N/A')}")
        print(f"   🌐 Social: {result.get('social_media', {}).get('platform', 'N/A')} - {result.get('social_media', {}).get('url', 'N/A')[:50]}...")
        print(f"   📄 Page Title: {result.get('page_title', 'N/A')[:80]}...")
        print(f"   🏷️  Meta Description: {result.get('meta_description', 'N/A')[:100]}...")
        print(f"   🏢 Company Name: {result.get('company_name', 'N/A')}")
        print(f"   📝 About Text: {result.get('about_text', 'N/A')[:150]}...")
        print(f"   🛠️  Services: {result.get('services_text', 'N/A')[:150]}...")
        print(f"   🎯 Mission/Vision: {result.get('mission_vision_text', 'N/A')[:100]}...")
        print(f"   📍 Location: {result.get('location_text', 'N/A')[:80]}...")
        print(f"   🏷️  Industry Keywords: {result.get('industry_keywords', 'N/A')}")
        print(f"   📋 Business Summary: {result.get('business_summary', 'N/A')[:200]}...")
        print(f"   📊 Key Headings: {result.get('key_headings', 'N/A')[:100]}...")
        print("-" * 80)
    
    # Show extraction statistics
    if successful > 0:
        print(f"\n📈 EXTRACTION STATISTICS:")
        
        # Count how many had each type of info
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media', {}).get('platform')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        services_found = len([r for r in results if 'error' not in r and r.get('services_text')])
        mission_found = len([r for r in results if 'error' not in r and r.get('mission_vision_text')])
        location_found = len([r for r in results if 'error' not in r and r.get('location_text')])
        
        print(f"   📧 Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones found: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        print(f"   📝 About sections found: {about_found}/{successful} ({about_found/successful*100:.1f}%)")
        print(f"   🛠️  Services found: {services_found}/{successful} ({services_found/successful*100:.1f}%)")
        print(f"   🎯 Mission/Vision found: {mission_found}/{successful} ({mission_found/successful*100:.1f}%)")
        print(f"   📍 Location info found: {location_found}/{successful} ({location_found/successful*100:.1f}%)")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"business_websites_enhanced_{timestamp}.csv"
    extractor.export_enhanced_to_csv(results, filename)
    
    print(f"\n🎉 Business website extraction complete!")
    print(f"💾 Results saved to: {filename}")
    
    # Show what this means for lead generation
    print(f"\n💡 LEAD GENERATION INSIGHTS:")
    print(f"   🎯 This enhanced extractor captures comprehensive business intelligence")
    print(f"   📊 You'll know what each company does, their services, mission, etc.")
    print(f"   🔍 Much better lead qualification than just contact info")
    print(f"   📈 Can segment leads by industry, services, company size, etc.")
    print(f"   🚀 Ready to use on your Pittsburgh coffee prospects!")


if __name__ == "__main__":
    asyncio.run(test_real_business_websites())
