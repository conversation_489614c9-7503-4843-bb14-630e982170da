"""
Hybrid Contact + Business Extractor
Prioritizes contact extraction (emails, phones, social) while also capturing business intelligence.
Uses the proven contact extraction logic from the original extractor.
"""

import asyncio
import pandas as pd
import re
import json
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor
from crawl4ai import <PERSON><PERSON>lerRunConfig, JsonCssExtractionStrategy


class HybridContactBusinessExtractor(PerfectContactExtractor):
    """Hybrid extractor that prioritizes contact extraction while adding business intelligence."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings."""
        super().__init__(batch_size, max_concurrent)
    
    def _get_business_info_schema(self):
        """Lightweight business info schema (secondary to contact extraction)."""
        return {
            "name": "Business Information",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "page_title",
                    "selector": "title",
                    "type": "text"
                },
                {
                    "name": "meta_description", 
                    "selector": "meta[name='description']",
                    "type": "attribute",
                    "attribute": "content"
                },
                {
                    "name": "company_names",
                    "selector": "h1, .company-name, .business-name, .brand-name, .logo-text",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "about_content",
                    "selector": "[class*='about'], [id*='about'], .about-us, .company-info, .our-story",
                    "type": "list", 
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "services_content",
                    "selector": "[class*='service'], .services, .what-we-do, .offerings, .products",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                },
                {
                    "name": "headings",
                    "selector": "h1, h2, h3",
                    "type": "list",
                    "fields": [{"name": "text", "type": "text"}]
                }
            ]
        }
    
    async def _extract_from_page_hybrid(self, crawler, url: str) -> dict:
        """Hybrid extraction: prioritize contacts, add business info."""
        try:
            # STEP 1: Use the original perfect contact extraction (proven to work)
            contact_data = await self._extract_from_page_perfect(crawler, url)
            
            # STEP 2: Add business intelligence with separate extraction
            business_data = await self._extract_business_info(crawler, url)
            
            # STEP 3: Merge results, prioritizing contact data
            result = {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                **contact_data,  # Contact data takes priority
                **business_data  # Business data is secondary
            }
            
            return result
            
        except Exception as e:
            print(f"❌ Hybrid extraction failed for {url}: {str(e)}")
            return {"url": url, "error": f"Extraction failed: {str(e)}"}
    
    async def _extract_business_info(self, crawler, url: str) -> dict:
        """Extract business information using lightweight schema."""
        try:
            # Use business info schema
            schema = self._get_business_info_schema()
            strategy = JsonCssExtractionStrategy(schema, verbose=False)
            config = CrawlerRunConfig(extraction_strategy=strategy)
            
            result = await crawler.arun(url=url, config=config)
            
            if result.success and result.extracted_content:
                extracted_data = json.loads(result.extracted_content)
                
                # Handle list format
                if isinstance(extracted_data, list) and len(extracted_data) > 0:
                    extracted_data = extracted_data[0]
                elif isinstance(extracted_data, list):
                    extracted_data = {}
                
                # Process business data
                return self._process_business_data(extracted_data)
            
        except Exception as e:
            print(f"⚠️  Business info extraction failed for {url}: {str(e)}")
        
        return {}
    
    def _process_business_data(self, raw_data: dict) -> dict:
        """Process and clean business data."""
        business_info = {}
        
        # Page title and meta description
        business_info['page_title'] = raw_data.get('page_title', '').strip()
        business_info['meta_description'] = raw_data.get('meta_description', '').strip()
        
        # Company name (take first valid one)
        company_names = [c.get('text', '').strip() for c in raw_data.get('company_names', []) if c.get('text', '').strip()]
        business_info['company_name'] = company_names[0] if company_names else ''
        
        # About content
        about_texts = [a.get('text', '').strip() for a in raw_data.get('about_content', []) if a.get('text', '').strip()]
        business_info['about_text'] = self._combine_text(about_texts, max_length=800)
        
        # Services content
        service_texts = [s.get('text', '').strip() for s in raw_data.get('services_content', []) if s.get('text', '').strip()]
        business_info['services_text'] = self._combine_text(service_texts, max_length=600)
        
        # Key headings
        headings = [h.get('text', '').strip() for h in raw_data.get('headings', []) if h.get('text', '').strip()]
        business_info['key_headings'] = ' | '.join(headings[:8])  # Top 8 headings
        
        # Generate business summary
        business_info['business_summary'] = self._generate_summary(business_info)
        
        return business_info
    
    def _combine_text(self, texts: list, max_length: int = 500) -> str:
        """Combine and clean text snippets."""
        if not texts:
            return ''
        
        # Combine texts
        combined = ' '.join(texts)
        
        # Clean up
        combined = re.sub(r'\s+', ' ', combined)  # Multiple spaces to single
        combined = re.sub(r'\n+', ' ', combined)  # Newlines to spaces
        combined = combined.strip()
        
        # Truncate if too long
        if len(combined) > max_length:
            combined = combined[:max_length].rsplit(' ', 1)[0] + '...'
        
        return combined
    
    def _generate_summary(self, business_info: dict) -> str:
        """Generate a concise business summary."""
        summary_parts = []
        
        # Add company name
        if business_info.get('company_name'):
            summary_parts.append(business_info['company_name'])
        
        # Add description
        for field in ['meta_description', 'about_text']:
            if business_info.get(field) and len(business_info[field]) > 20:
                summary_parts.append(business_info[field][:150])
                break
        
        # Add services if available
        if business_info.get('services_text') and len(business_info['services_text']) > 20:
            summary_parts.append(f"Services: {business_info['services_text'][:100]}")
        
        return ' | '.join(summary_parts)
    
    async def extract_hybrid_data(self, urls: list) -> list:
        """Extract hybrid contact + business data from URLs."""
        print(f"🔥 Starting HYBRID extraction for {len(urls)} URLs...")
        print(f"   📞 PRIORITY: Contact extraction (emails, phones, social)")
        print(f"   🏢 SECONDARY: Business intelligence")
        
        results = []
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            print(f"📊 Processing batch {i//self.batch_size + 1}: {len(batch)} URLs")
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def process_url(url):
                async with semaphore:
                    from crawl4ai import AsyncWebCrawler
                    async with AsyncWebCrawler() as crawler:
                        return await self._extract_from_page_hybrid(crawler, url)
            
            # Process batch concurrently
            batch_results = await asyncio.gather(*[process_url(url) for url in batch])
            results.extend(batch_results)
            
            # Progress update
            successful = len([r for r in batch_results if 'error' not in r])
            print(f"✅ Batch complete: {successful}/{len(batch)} successful")
        
        return results
    
    def export_hybrid_to_csv(self, results: list, filename: str):
        """Export hybrid results to CSV."""
        if not results:
            print("❌ No results to export")
            return
        
        # Flatten the results for CSV
        flattened = []
        for result in results:
            if 'error' not in result:
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    # CONTACT INFO (priority)
                    'email': result.get('email', ''),
                    'email_confidence': result.get('email_confidence', ''),
                    'phone': result.get('phone', ''),
                    'phone_confidence': result.get('phone_confidence', ''),
                    'social_media': result.get('social_media', ''),
                    'pages_checked': result.get('pages_checked', ''),
                    # BUSINESS INFO (secondary)
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })
        
        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Hybrid results exported to: {filename}")


async def test_hybrid_extractor():
    """Test the hybrid extractor on first 20 prospects."""
    print("🔥 TESTING HYBRID CONTACT + BUSINESS EXTRACTOR")
    print("=" * 60)
    print("This prioritizes contact extraction while adding business intelligence!")
    
    # Load first 20 prospects for quick test
    try:
        df = pd.read_csv('first_100_prospects_20250915_002842.csv')
        urls = df['website'].dropna().tolist()[:20]  # Test with 20 URLs
        print(f"✅ Testing with {len(urls)} prospect URLs")
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return
    
    # Create hybrid extractor with optimized settings
    extractor = HybridContactBusinessExtractor(batch_size=20, max_concurrent=30)
    
    print(f"🚀 HYBRID EXTRACTOR SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Strategy: Contact extraction FIRST, business info SECOND")
    
    start_time = datetime.now()
    
    # Extract hybrid data
    results = await extractor.extract_hybrid_data(urls)
    
    duration = (datetime.now() - start_time).total_seconds()
    
    # Analyze results
    successful = len([r for r in results if 'error' not in r])
    
    print(f"\n📊 HYBRID EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    
    # Analyze contact extraction success
    if successful > 0:
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        company_names = len([r for r in results if 'error' not in r and r.get('company_name')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        
        print(f"\n🎯 CONTACT EXTRACTION SUCCESS:")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        
        print(f"\n🏢 BUSINESS INFO SUCCESS:")
        print(f"   🏢 Company names: {company_names}/{successful} ({company_names/successful*100:.1f}%)")
        print(f"   📝 About sections: {about_found}/{successful} ({about_found/successful*100:.1f}%)")
    
    # Show sample results
    print(f"\n📋 SAMPLE HYBRID RESULTS:")
    print("=" * 80)
    
    for i, result in enumerate([r for r in results if 'error' not in r][:5], 1):
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        print(f"   📧 Email: {result.get('email', 'N/A')} (confidence: {result.get('email_confidence', 'N/A')})")
        print(f"   📞 Phone: {result.get('phone', 'N/A')} (confidence: {result.get('phone_confidence', 'N/A')})")
        print(f"   🌐 Social: {result.get('social_media', 'N/A')}")
        print(f"   📄 Pages checked: {result.get('pages_checked', 'N/A')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print(f"   📝 About: {result.get('about_text', 'N/A')[:100]}...")
        print(f"   🛠️  Services: {result.get('services_text', 'N/A')[:100]}...")
        print("-" * 80)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hybrid_extraction_test_{timestamp}.csv"
    extractor.export_hybrid_to_csv(results, filename)
    
    print(f"\n🎉 HYBRID EXTRACTION TEST COMPLETE!")
    print(f"💾 Results saved to: {filename}")
    
    # Compare with previous results
    print(f"\n💡 EXPECTED IMPROVEMENTS:")
    print(f"   📧 Email extraction should be 60-80% (was 21%)")
    print(f"   📞 Phone extraction should be 70-90% (was 67%)")
    print(f"   🌐 Social extraction should be 60-80% (was 67%)")
    print(f"   🏢 Still get business intelligence as bonus!")


if __name__ == "__main__":
    asyncio.run(test_hybrid_extractor())
