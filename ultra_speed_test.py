"""
ULTRA SPEED Test Script
Pushes the scraper to absolute maximum speed with advanced optimizations.
"""

import asyncio
import pandas as pd
import sys
import os
from datetime import datetime

from aganl.perfect_contact_extractor import PerfectContactExtractor


class UltraSpeedExtractor(PerfectContactExtractor):
    """Ultra-speed version with aggressive optimizations."""
    
    def __init__(self, batch_size: int = 100, max_concurrent: int = 50):
        """Initialize with INSANE settings."""
        super().__init__(batch_size, max_concurrent)
        
        # SPEED OPTIMIZATIONS:
        # 1. Reduce pages checked - only check main + contact pages
        self.priority_pages = [
            ('', 10),              # Main page only
            ('/contact', 9),       # Just one contact page
        ]
        
        # 2. Enable early stopping after finding ANY contact info
        self.aggressive_early_stop = True
    
    async def _check_single_page(self, url: str, page_path: str, priority: int, crawler):
        """Optimized single page checking with faster timeouts."""
        full_url = url.rstrip('/') + page_path
        
        try:
            # SPEED HACK: Reduce timeout to 5 seconds (was 15)
            config = CrawlerRunConfig(
                word_count_threshold=1,
                extraction_strategy=JsonCssExtractionStrategy(self._get_extraction_schema()),
                chunking_strategy=None,
                bypass_cache=True,
                page_timeout=5000,  # 5 seconds instead of 15
                delay_before_return_html=0.1  # Minimal delay
            )
            
            result = await crawler.arun(url=full_url, config=config)
            
            if result.success and result.extracted_content:
                extracted_data = json.loads(result.extracted_content)
                contacts = self._extract_contacts_from_data(extracted_data, full_url)
                
                # AGGRESSIVE EARLY STOP: Stop after finding ANY contact info
                if self.aggressive_early_stop and (contacts.get('email') or contacts.get('phone') or contacts.get('social_media')):
                    return contacts, True  # Signal to stop checking more pages
                
                return contacts, False
            
        except Exception as e:
            pass
        
        return {}, False


async def test_ultra_speed(num_urls=50):
    """Test with ULTRA speed settings."""
    print(f"🔥 ULTRA SPEED TEST ({num_urls} URLs)")
    print("=" * 50)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:num_urls]
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # ULTRA SPEED SETTINGS
    extractor = UltraSpeedExtractor(
        batch_size=100,     # Process 100 URLs per batch
        max_concurrent=50   # 50 concurrent requests - INSANE!
    )
    
    print(f"🔥 ULTRA SPEED SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Reduced pages: Only main + contact (not contact-us + about)")
    print(f"   • Aggressive early stop: Stop after ANY contact found")
    print(f"   • Faster timeouts: 5 seconds per page (not 15)")
    print(f"   • This will DEMOLISH websites! 💥")
    
    print(f"\n🚀 Starting ULTRA speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        rate = len(urls) / duration
        successful = len([r for r in results if 'error' not in r])
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        print(f"\n📊 ULTRA SPEED RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {rate:.2f} URLs/second")
        print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        print(f"📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "📧 Emails: 0/0")
        print(f"🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "🌐 Social: 0/0")
        
        # Calculate projections for your main dataset
        main_dataset_size = 8004
        projected_time = main_dataset_size / rate
        
        print(f"\n🔮 MAIN DATASET PROJECTION:")
        print(f"   • At this rate: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        print(f"   • That's {main_dataset_size} URLs in {projected_time:.0f} seconds!")
        print(f"   • BLAZING FAST! 🔥🔥🔥")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ultra_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        print(f"💾 Results saved to: {filename}")
        
        return True, rate
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, 0


async def test_parallel_batches():
    """Test running multiple batches in parallel - MAXIMUM CHAOS MODE."""
    print(f"\n💀 PARALLEL BATCHES TEST - MAXIMUM CHAOS MODE")
    print("=" * 50)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        all_urls = df['URL'].dropna().tolist()[:80]  # Use 80 URLs
        print(f"✅ Loaded {len(all_urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # Split URLs into 4 batches and run them ALL AT THE SAME TIME
    batch_size = len(all_urls) // 4
    batches = [
        all_urls[i:i+batch_size] 
        for i in range(0, len(all_urls), batch_size)
    ]
    
    print(f"💀 CHAOS MODE SETTINGS:")
    print(f"   • Running {len(batches)} batches SIMULTANEOUSLY")
    print(f"   • Each batch: ~{batch_size} URLs")
    print(f"   • Each batch: 30 concurrent requests")
    print(f"   • Total concurrent: {len(batches) * 30} requests!")
    print(f"   • This will NUKE the internet! ☢️")
    
    print(f"\n🚀 Starting CHAOS MODE extraction...")
    start_time = datetime.now()
    
    try:
        # Create multiple extractors and run them in parallel
        extractors = [
            PerfectContactExtractor(batch_size=batch_size, max_concurrent=30)
            for _ in batches
        ]
        
        # Run all batches simultaneously
        tasks = [
            extractor.extract_perfect(batch_urls)
            for extractor, batch_urls in zip(extractors, batches)
        ]
        
        batch_results = await asyncio.gather(*tasks)
        
        # Combine all results
        all_results = []
        for batch_result in batch_results:
            all_results.extend(batch_result)
        
        duration = (datetime.now() - start_time).total_seconds()
        
        rate = len(all_results) / duration
        successful = len([r for r in all_results if 'error' not in r])
        
        print(f"\n📊 CHAOS MODE RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {rate:.2f} URLs/second")
        print(f"✅ Success: {successful}/{len(all_results)} ({successful/len(all_results)*100:.1f}%)")
        print(f"💀 CHAOS LEVEL: MAXIMUM!")
        
        # Calculate projections
        main_dataset_size = 8004
        projected_time = main_dataset_size / rate
        
        print(f"\n🔮 MAIN DATASET PROJECTION:")
        print(f"   • At this rate: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        print(f"   • ABSOLUTE SPEED DEMON! 👹")
        
        return True, rate
        
    except Exception as e:
        print(f"❌ CHAOS MODE failed: {e}")
        import traceback
        traceback.print_exc()
        return False, 0


async def main():
    """Main ultra-speed test function."""
    print("💥 ULTRA SPEED TESTING SUITE")
    print("=" * 60)
    print("WARNING: These tests will hit websites EXTREMELY hard!")
    print("This pushes the scraper to absolute maximum speed.")
    print("=" * 60)
    
    speeds = []
    
    try:
        # Test 1: Ultra speed
        print("TEST 1: Ultra speed with optimizations")
        success, rate = await test_ultra_speed(50)
        if success:
            speeds.append(("Ultra Speed", rate))
            print(f"✅ Ultra speed successful! Rate: {rate:.2f} URLs/sec")
        
        print("\nPress Enter to continue to CHAOS MODE...")
        input()
        
        # Test 2: Parallel batches (CHAOS MODE)
        print("\nTEST 2: Parallel batches - CHAOS MODE")
        success, rate = await test_parallel_batches()
        if success:
            speeds.append(("CHAOS MODE", rate))
            print(f"✅ CHAOS MODE successful! Rate: {rate:.2f} URLs/sec")
        
        # Summary
        if speeds:
            print(f"\n🏆 SPEED COMPARISON:")
            print("=" * 50)
            for test_name, rate in speeds:
                print(f"   • {test_name}: {rate:.2f} URLs/second")
            
            fastest = max(speeds, key=lambda x: x[1])
            print(f"\n👑 FASTEST METHOD: {fastest[0]} at {fastest[1]:.2f} URLs/second")
            
            # Final projection
            main_dataset_size = 8004
            time_for_main = main_dataset_size / fastest[1]
            print(f"\n🎯 YOUR MAIN DATASET ({main_dataset_size} URLs):")
            print(f"   • Time needed: {time_for_main/60:.1f} minutes ({time_for_main/3600:.1f} hours)")
            print(f"   • That's INSANELY FAST! 🚀🚀🚀")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Ultra speed testing interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
