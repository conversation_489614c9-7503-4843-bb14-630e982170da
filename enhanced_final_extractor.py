"""
Enhanced Final Contact + Business Extractor
Maximum email/phone extraction with validation and contact form detection.
"""

import asyncio
import pandas as pd
import re
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


class EnhancedFinalExtractor(PerfectContactExtractor):
    """Enhanced extractor with improved validation and contact form detection."""
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 30):
        """Initialize with optimized settings."""
        super().__init__(batch_size, max_concurrent)
        
        # Enhanced email patterns
        self.email_patterns = [
            # Standard email pattern
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            # Mailto links
            r'mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            # Emails in JavaScript
            r'["\']([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})["\']',
            # Data attributes
            r'data-email=["\']([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})["\']',
            # Obfuscated emails (at -> @, dot -> .)
            r'\b([A-Za-z0-9._%+-]+)\s*(?:at|AT)\s*([A-Za-z0-9.-]+)\s*(?:dot|DOT)\s*([A-Z|a-z]{2,})\b',
        ]
        
        # Enhanced phone patterns
        self.phone_patterns = [
            # US phone formats
            r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b',
            # International format
            r'\+[1-9]\d{1,14}\b',
            # Phone with extensions
            r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})(?:\s*(?:ext|x|extension)\.?\s*\d+)?\b',
            # Toll-free numbers
            r'\b(?:1[-.\s]?)?(?:800|888|877|866|855|844|833|822)[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
        ]
        
        # Contact form indicators
        self.contact_form_patterns = [
            r'<form[^>]*(?:contact|email|inquiry|message)[^>]*>',
            r'<input[^>]*(?:type=["\']email["\']|name=["\']email["\'])',
            r'<textarea[^>]*(?:message|inquiry|comment)[^>]*>',
            r'class=["\'][^"\']*(?:contact-form|email-form|inquiry-form)[^"\']*["\']',
        ]
    
    async def extract_enhanced_data(self, urls: list) -> list:
        """Extract enhanced contact + business data with validation."""
        print(f"🔥 Starting ENHANCED extraction for {len(urls)} URLs...")
        print(f"   📞 ENHANCED: Better email/phone patterns + validation")
        print(f"   📝 NEW: Contact form detection")
        print(f"   ✅ IMPROVED: Data quality validation")
        
        # Use the original perfect extraction as base
        original_results = await self.extract_perfect(urls)
        
        # Enhance each result
        print(f"🔍 Enhancing {len(original_results)} results with better extraction...")
        
        enhanced_results = []
        for i, result in enumerate(original_results):
            if 'error' not in result:
                enhanced_result = await self._enhance_extraction(result)
                enhanced_results.append(enhanced_result)
            else:
                enhanced_results.append(result)
            
            if (i + 1) % 10 == 0:
                print(f"   ✅ Enhanced {i + 1}/{len(original_results)} results")
        
        return enhanced_results
    
    async def _enhance_extraction(self, result: dict) -> dict:
        """Enhance a single extraction result."""
        url = result.get('url', '')
        
        try:
            # Get fresh page content for enhanced extraction
            from crawl4ai import AsyncWebCrawler
            async with AsyncWebCrawler() as crawler:
                from crawl4ai import CrawlerRunConfig
                config = CrawlerRunConfig()
                page_result = await crawler.arun(url=url, config=config)
                
                if page_result.success and page_result.cleaned_html:
                    # Enhanced email extraction
                    enhanced_email = self._extract_enhanced_email(page_result.cleaned_html)
                    
                    # Enhanced phone extraction
                    enhanced_phone = self._extract_enhanced_phone(page_result.cleaned_html)
                    
                    # Contact form detection
                    contact_form_detected = self._detect_contact_form(page_result.cleaned_html)
                    
                    # Business info extraction
                    business_info = self._extract_business_from_html(page_result.cleaned_html, url)
                    
                    # Create enhanced result with clean data
                    enhanced_result = {
                        **result,  # Keep original data
                        **business_info,  # Add business info
                        # Enhanced contact data
                        'email': enhanced_email['email'],
                        'email_confidence': enhanced_email['confidence'],
                        'email_status': enhanced_email['status'],
                        'phone': enhanced_phone['phone'],
                        'phone_confidence': enhanced_phone['confidence'],
                        'phone_status': enhanced_phone['status'],
                        'contact_form_detected': contact_form_detected,
                        # Clean social media data
                        'social_media': self._clean_social_media(result.get('social_media')),
                    }
                    
                    return enhanced_result
                
        except Exception as e:
            print(f"⚠️  Enhanced extraction failed for {url}: {str(e)}")
        
        # Fallback: clean the original result
        return self._clean_original_result(result)
    
    def _extract_enhanced_email(self, html_content: str) -> dict:
        """Extract email with enhanced patterns and validation."""
        emails_found = []
        
        # Try each email pattern
        for pattern in self.email_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    # Handle obfuscated emails (at/dot pattern)
                    if len(match) == 3:
                        email = f"{match[0]}@{match[1]}.{match[2]}"
                    else:
                        email = match[0] if match[0] else match[1]
                else:
                    email = match
                
                # Validate email
                if self._is_valid_email(email):
                    emails_found.append(email.lower())
        
        # Remove duplicates and filter
        emails_found = list(set(emails_found))
        emails_found = [e for e in emails_found if self._is_business_email(e)]
        
        if emails_found:
            # Select best email
            best_email = self._select_best_email(emails_found)
            return {
                'email': best_email,
                'confidence': 0.9,
                'status': 'found'
            }
        
        return {
            'email': '',
            'confidence': 0,
            'status': 'not_found'
        }
    
    def _extract_enhanced_phone(self, html_content: str) -> dict:
        """Extract phone with enhanced patterns and validation."""
        phones_found = []
        
        # Try each phone pattern
        for pattern in self.phone_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if isinstance(match, tuple):
                    # Reconstruct phone from groups
                    if len(match) == 3:
                        phone = f"({match[0]}) {match[1]}-{match[2]}"
                    else:
                        phone = ''.join(match)
                else:
                    phone = match
                
                # Clean and validate phone
                clean_phone = self._clean_phone_number(phone)
                if self._is_valid_phone(clean_phone):
                    phones_found.append(clean_phone)
        
        # Remove duplicates
        phones_found = list(set(phones_found))
        
        if phones_found:
            # Select best phone (prefer formatted ones)
            best_phone = self._select_best_phone(phones_found)
            return {
                'phone': best_phone,
                'confidence': 0.9,
                'status': 'found'
            }
        
        return {
            'phone': '',
            'confidence': 0,
            'status': 'not_found'
        }
    
    def _detect_contact_form(self, html_content: str) -> bool:
        """Detect if page has contact forms."""
        for pattern in self.contact_form_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return True
        return False
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        if not email or len(email) < 5:
            return False
        
        # Basic email validation
        pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
        return bool(re.match(pattern, email))
    
    def _is_business_email(self, email: str) -> bool:
        """Check if email looks like a business email."""
        # Filter out common non-business emails
        excluded_domains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'aol.com', 'icloud.com', 'live.com', 'msn.com'
        ]
        
        domain = email.split('@')[1].lower() if '@' in email else ''
        return domain not in excluded_domains
    
    def _select_best_email(self, emails: list) -> str:
        """Select the best email from candidates."""
        # Prefer emails with common business patterns
        business_patterns = ['info@', 'contact@', 'admin@', 'office@', 'hello@']
        
        for pattern in business_patterns:
            for email in emails:
                if email.startswith(pattern):
                    return email
        
        # Return first valid email
        return emails[0] if emails else ''
    
    def _clean_phone_number(self, phone: str) -> str:
        """Clean and format phone number."""
        # Remove all non-digit characters except +
        digits = re.sub(r'[^\d+]', '', phone)
        
        # Handle US numbers
        if digits.startswith('1') and len(digits) == 11:
            digits = digits[1:]  # Remove country code
        
        if len(digits) == 10:
            # Format as (XXX) XXX-XXXX
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif digits.startswith('+'):
            return digits  # Keep international format
        
        return phone  # Return original if can't format
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False
        
        # Check for minimum length and digits
        digits_only = re.sub(r'[^\d]', '', phone)
        return len(digits_only) >= 10 and len(digits_only) <= 15
    
    def _select_best_phone(self, phones: list) -> str:
        """Select the best phone from candidates."""
        # Prefer formatted phones
        formatted = [p for p in phones if '(' in p and ')' in p and '-' in p]
        if formatted:
            return formatted[0]
        
        return phones[0] if phones else ''
    
    def _clean_social_media(self, social_data) -> str:
        """Clean social media data."""
        if not social_data:
            return ''
        
        if isinstance(social_data, dict):
            platform = social_data.get('platform', '')
            url = social_data.get('url', '')
            if platform and url:
                return f"{platform}: {url}"
            return url
        
        return str(social_data)
    
    def _clean_original_result(self, result: dict) -> dict:
        """Clean original result data."""
        # Clean email
        email = result.get('email')
        if isinstance(email, dict):
            email = email.get('email', '')
        email = email if email and email != 'None' else ''
        
        # Clean phone
        phone = result.get('phone')
        if isinstance(phone, dict):
            phone = phone.get('phone', '')
        phone = phone if phone and phone != 'None' else ''
        
        return {
            **result,
            'email': email,
            'email_confidence': result.get('email_confidence', 0),
            'email_status': 'found' if email else 'not_found',
            'phone': phone,
            'phone_confidence': result.get('phone_confidence', 0),
            'phone_status': 'found' if phone else 'not_found',
            'contact_form_detected': False,
            'social_media': self._clean_social_media(result.get('social_media')),
        }
    
    def _extract_business_from_html(self, html_content: str, url: str) -> dict:
        """Extract business information from HTML content."""
        business_info = {}
        
        # Page title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        business_info['page_title'] = title_match.group(1).strip() if title_match else ''
        
        # Meta description
        meta_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        business_info['meta_description'] = meta_match.group(1).strip() if meta_match else ''
        
        # Company name extraction
        business_info['company_name'] = self._extract_company_name(html_content)
        
        # About text
        business_info['about_text'] = self._extract_about_text(html_content)
        
        # Services text
        business_info['services_text'] = self._extract_services_text(html_content)
        
        # Key headings
        business_info['key_headings'] = self._extract_headings(html_content)
        
        # Business summary
        business_info['business_summary'] = self._generate_business_summary(business_info)
        
        return business_info

    def _extract_company_name(self, html_content: str) -> str:
        """Extract company name using multiple strategies."""
        strategies = [
            # H1 tags
            r'<h1[^>]*>([^<]+)</h1>',
            # Title tag (before | or -)
            r'<title[^>]*>([^<|\\-]+)',
            # Company/brand class names
            r'class=["\'][^"\']*(?:company|brand|business)[^"\']*["\'][^>]*>([^<]+)',
            # Logo alt text
            r'<img[^>]*alt=["\']([^"\']*(?:logo|company|brand)[^"\']*)["\']',
            # Organization schema
            r'"@type":\s*"Organization"[^}]*"name":\s*"([^"]+)"',
        ]

        for pattern in strategies:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                candidate = match.strip()
                if (len(candidate) > 2 and len(candidate) < 100 and
                    not any(word in candidate.lower() for word in ['home', 'welcome', 'contact', 'about', 'menu'])):
                    return candidate

        return ''

    def _extract_about_text(self, html_content: str) -> str:
        """Extract about/company description text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*about[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)about\s+us[^<]*<[^>]*>([^<]+)',
            r'(?i)(?:our\s+)?(?:company|story|mission)[^<]*<[^>]*>([^<]+)',
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']',
        ]

        about_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 20 and len(text) < 500:
                    about_texts.append(text)

        return ' '.join(about_texts[:2])[:400] if about_texts else ''

    def _extract_services_text(self, html_content: str) -> str:
        """Extract services/products text."""
        patterns = [
            r'<[^>]*(?:class|id)=["\'][^"\']*service[^"\']*["\'][^>]*>([^<]+)',
            r'(?i)what\s+we\s+do[^<]*<[^>]*>([^<]+)',
            r'(?i)(?:our\s+)?(?:services|products|offerings)[^<]*<[^>]*>([^<]+)',
        ]

        service_texts = []
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                text = re.sub(r'\s+', ' ', match.strip())
                if len(text) > 10 and len(text) < 300:
                    service_texts.append(text)

        return ' '.join(service_texts[:2])[:300] if service_texts else ''

    def _extract_headings(self, html_content: str) -> str:
        """Extract key headings."""
        heading_matches = re.findall(r'<h[1-3][^>]*>([^<]+)</h[1-3]>', html_content, re.IGNORECASE)
        headings = []
        for heading in heading_matches:
            clean_heading = re.sub(r'\s+', ' ', heading.strip())
            if len(clean_heading) > 2 and len(clean_heading) < 80:
                headings.append(clean_heading)

        return ' | '.join(headings[:6])

    def _generate_business_summary(self, business_info: dict) -> str:
        """Generate business summary."""
        parts = []

        if business_info.get('company_name'):
            parts.append(business_info['company_name'])

        if business_info.get('meta_description'):
            parts.append(business_info['meta_description'][:100])
        elif business_info.get('about_text'):
            parts.append(business_info['about_text'][:100])

        if business_info.get('services_text'):
            parts.append(f"Services: {business_info['services_text'][:80]}")

        return ' | '.join(parts)

    def export_enhanced_to_csv(self, results: list, filename: str):
        """Export enhanced results to CSV with clean formatting."""
        if not results:
            print("❌ No results to export")
            return

        # Flatten results for CSV
        flattened = []
        for result in results:
            if 'error' not in result:
                flattened.append({
                    'url': result.get('url', ''),
                    'timestamp': result.get('timestamp', ''),
                    # ENHANCED CONTACT INFO
                    'email': result.get('email', ''),
                    'email_confidence': result.get('email_confidence', ''),
                    'email_status': result.get('email_status', ''),
                    'phone': result.get('phone', ''),
                    'phone_confidence': result.get('phone_confidence', ''),
                    'phone_status': result.get('phone_status', ''),
                    'social_media': result.get('social_media', ''),
                    'contact_form_detected': result.get('contact_form_detected', False),
                    'pages_checked': result.get('pages_checked', ''),
                    # BUSINESS INFO
                    'page_title': result.get('page_title', ''),
                    'meta_description': result.get('meta_description', ''),
                    'company_name': result.get('company_name', ''),
                    'about_text': result.get('about_text', ''),
                    'services_text': result.get('services_text', ''),
                    'key_headings': result.get('key_headings', ''),
                    'business_summary': result.get('business_summary', '')
                })

        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        print(f"💾 Enhanced results exported to: {filename}")


async def test_enhanced_extractor():
    """Test the enhanced extractor on first 20 prospects."""
    print("🚀 TESTING ENHANCED FINAL EXTRACTOR")
    print("=" * 60)
    print("Maximum email/phone extraction + validation + contact form detection!")

    # Load first 20 prospects
    try:
        df = pd.read_csv('first_100_prospects_20250915_002842.csv')
        urls = df['website'].dropna().tolist()[:20]  # Test with 20 URLs
        print(f"✅ Testing with {len(urls)} prospect URLs")
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return

    # Create enhanced extractor
    extractor = EnhancedFinalExtractor(batch_size=20, max_concurrent=30)

    print(f"🚀 ENHANCED EXTRACTOR SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Enhanced email patterns: {len(extractor.email_patterns)}")
    print(f"   • Enhanced phone patterns: {len(extractor.phone_patterns)}")
    print(f"   • Contact form detection: ✅")
    print(f"   • Data validation: ✅")

    start_time = datetime.now()

    # Extract enhanced data
    results = await extractor.extract_enhanced_data(urls)

    duration = (datetime.now() - start_time).total_seconds()

    # Analyze results
    successful = len([r for r in results if 'error' not in r])

    print(f"\n📊 ENHANCED EXTRACTION RESULTS:")
    print(f"⏱️  Time: {duration:.1f} seconds")
    print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
    print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")

    # Analyze enhanced extraction success
    if successful > 0:
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        contact_forms = len([r for r in results if 'error' not in r and r.get('contact_form_detected')])
        company_names = len([r for r in results if 'error' not in r and r.get('company_name')])
        about_found = len([r for r in results if 'error' not in r and r.get('about_text')])
        pages_checked_avg = sum(r.get('pages_checked', 0) for r in results if 'error' not in r) / successful

        print(f"\n🎯 ENHANCED CONTACT EXTRACTION:")
        print(f"   📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
        print(f"   📞 Phones: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
        print(f"   🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
        print(f"   📝 Contact forms: {contact_forms}/{successful} ({contact_forms/successful*100:.1f}%)")
        print(f"   📄 Avg pages checked: {pages_checked_avg:.1f}")

        print(f"\n🏢 BUSINESS INFO SUCCESS:")
        print(f"   🏢 Company names: {company_names}/{successful} ({company_names/successful*100:.1f}%)")
        print(f"   📝 About sections: {about_found}/{successful} ({about_found/successful*100:.1f}%)")

    # Show sample results
    print(f"\n📋 SAMPLE ENHANCED RESULTS:")
    print("=" * 80)

    for i, result in enumerate([r for r in results if 'error' not in r][:5], 1):
        print(f"\n{i}. 🏢 {result.get('url', 'Unknown URL')}")
        print(f"   📧 Email: {result.get('email', 'N/A')} (status: {result.get('email_status', 'N/A')})")
        print(f"   📞 Phone: {result.get('phone', 'N/A')} (status: {result.get('phone_status', 'N/A')})")
        print(f"   🌐 Social: {result.get('social_media', 'N/A')}")
        print(f"   📝 Contact form: {result.get('contact_form_detected', False)}")
        print(f"   📄 Pages checked: {result.get('pages_checked', 'N/A')}")
        print(f"   🏢 Company: {result.get('company_name', 'N/A')}")
        print("-" * 80)

    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"enhanced_extraction_test_{timestamp}.csv"
    extractor.export_enhanced_to_csv(results, filename)

    print(f"\n🎉 ENHANCED EXTRACTION TEST COMPLETE!")
    print(f"💾 Results saved to: {filename}")

    print(f"\n💡 ENHANCED ACHIEVEMENTS:")
    print(f"   📧 Email extraction: {emails_found/successful*100:.1f}% (target: 60-80%)")
    print(f"   📞 Phone extraction: {phones_found/successful*100:.1f}% (target: 70-90%)")
    print(f"   📝 Contact form detection: {contact_forms/successful*100:.1f}%")
    print(f"   ✅ Data validation: All emails/phones validated")
    print(f"   🚀 Ready for full dataset!")


if __name__ == "__main__":
    asyncio.run(test_enhanced_extractor())
