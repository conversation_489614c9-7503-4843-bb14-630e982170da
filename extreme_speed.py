"""
EXTREME SPEED Script
Simple script with the most aggressive settings that actually work.
"""

import asyncio
import pandas as pd
from datetime import datetime
from aganl.perfect_contact_extractor import PerfectContactExtractor


async def test_extreme_speed():
    """Test with extreme speed settings."""
    print("🔥 EXTREME SPEED TEST")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:30]  # Test with 30 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    # EXTREME SETTINGS - Push it to the limit!
    extractor = PerfectContactExtractor(
        batch_size=50,      # Large batches
        max_concurrent=40   # VERY high concurrency
    )
    
    print(f"🔥 EXTREME SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • This will hit websites VERY hard!")
    
    print(f"\n🚀 Starting extreme speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        rate = len(urls) / duration
        successful = len([r for r in results if 'error' not in r])
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        print(f"\n📊 EXTREME SPEED RESULTS:")
        print(f"⏱️  Time: {duration:.1f} seconds")
        print(f"⚡ Rate: {rate:.2f} URLs/second")
        print(f"✅ Success: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        print(f"📧 Emails: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)" if successful > 0 else "📧 Emails: 0/0")
        print(f"🌐 Social: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)" if successful > 0 else "🌐 Social: 0/0")
        
        # Print detailed summary
        extractor.print_summary(results)
        
        # Calculate projections for your main dataset
        main_dataset_size = 8004
        projected_time = main_dataset_size / rate
        
        print(f"\n🎯 MAIN DATASET PROJECTION:")
        print(f"   • {main_dataset_size} URLs at {rate:.2f} URLs/sec")
        print(f"   • Total time: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        print(f"   • That's {projected_time:.0f} seconds total!")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"extreme_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        print(f"💾 Results saved to: {filename}")
        
        return rate
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def test_different_batch_sizes():
    """Test different batch sizes to find the sweet spot."""
    print("\n🧪 TESTING DIFFERENT BATCH SIZES")
    print("=" * 50)
    
    # Load URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        test_urls = df['URL'].dropna().tolist()[:20]  # Use 20 URLs for quick tests
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return
    
    # Test different configurations
    configs = [
        (20, 30),   # batch_size=20, concurrent=30
        (30, 35),   # batch_size=30, concurrent=35  
        (40, 40),   # batch_size=40, concurrent=40
        (50, 45),   # batch_size=50, concurrent=45
        (60, 50),   # batch_size=60, concurrent=50
    ]
    
    results = []
    
    for batch_size, concurrent in configs:
        print(f"\n🧪 Testing: batch_size={batch_size}, concurrent={concurrent}")
        
        extractor = PerfectContactExtractor(
            batch_size=batch_size,
            max_concurrent=concurrent
        )
        
        start_time = datetime.now()
        
        try:
            extraction_results = await extractor.extract_perfect(test_urls)
            duration = (datetime.now() - start_time).total_seconds()
            
            rate = len(test_urls) / duration
            successful = len([r for r in extraction_results if 'error' not in r])
            success_rate = successful / len(extraction_results) * 100
            
            print(f"   ✅ Rate: {rate:.2f} URLs/sec, Success: {success_rate:.1f}%, Time: {duration:.1f}s")
            
            results.append({
                'batch_size': batch_size,
                'concurrent': concurrent,
                'rate': rate,
                'success_rate': success_rate,
                'duration': duration
            })
            
        except Exception as e:
            print(f"   ❌ Failed: {str(e)[:50]}...")
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    if results:
        print(f"\n📊 BATCH SIZE TEST RESULTS:")
        print("=" * 60)
        print(f"{'Batch':<6} {'Concurrent':<10} {'Rate':<12} {'Success':<8} {'Time':<6}")
        print("-" * 60)
        
        for r in sorted(results, key=lambda x: x['rate'], reverse=True):
            print(f"{r['batch_size']:<6} {r['concurrent']:<10} {r['rate']:<12.2f} {r['success_rate']:<8.1f}% {r['duration']:<6.1f}s")
        
        best = max(results, key=lambda x: x['rate'])
        print(f"\n🏆 BEST CONFIGURATION:")
        print(f"   batch_size={best['batch_size']}, concurrent={best['concurrent']}")
        print(f"   Rate: {best['rate']:.2f} URLs/second")
        
        return best
    
    return None


async def main():
    """Main function."""
    print("🚀 EXTREME SPEED TESTING")
    print("=" * 50)
    print("This will test the most aggressive settings that work reliably.")
    print("=" * 50)
    
    try:
        # Test 1: Extreme speed
        extreme_rate = await test_extreme_speed()
        
        if extreme_rate > 0:
            print(f"\n✅ Extreme speed test completed: {extreme_rate:.2f} URLs/sec")
            
            # Ask if user wants to test different configurations
            print("\nWant to test different batch sizes to optimize further? (y/n)")
            response = input().lower().strip()
            
            if response == 'y':
                best_config = await test_different_batch_sizes()
                
                if best_config:
                    print(f"\n🎯 FINAL RECOMMENDATION:")
                    print(f"   Use: batch_size={best_config['batch_size']}, max_concurrent={best_config['concurrent']}")
                    print(f"   Expected rate: {best_config['rate']:.2f} URLs/second")
                    
                    # Final projection
                    main_dataset_size = 8004
                    time_needed = main_dataset_size / best_config['rate']
                    print(f"\n🔮 YOUR MAIN DATASET ({main_dataset_size} URLs):")
                    print(f"   • Time needed: {time_needed/60:.1f} minutes ({time_needed/3600:.1f} hours)")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"   1. Use these optimal settings in your main processing script")
        print(f"   2. Test with 20-50 URLs from your real dataset first")
        print(f"   3. Then scale up to the full 8,004 URLs")
        print(f"   4. Monitor your internet connection and system resources")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
