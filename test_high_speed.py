"""
High-Speed Test Script for Practice URLs
Tests the web scraper with aggressive settings for maximum speed.
"""

import asyncio
import pandas as pd
import sys
import os
from datetime import datetime

from aganl.perfect_contact_extractor import PerfectContactExtractor


async def test_high_speed_small():
    """Test with high-speed settings on 10 URLs."""
    print("🚀 HIGH-SPEED TEST (10 URLs)")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:10]  # First 10 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # HIGH-SPEED SETTINGS
    extractor = PerfectContactExtractor(
        batch_size=20,      # Process 20 URLs per batch (was 2)
        max_concurrent=10   # 10 concurrent requests (was 1)
    )
    
    print(f"⚡ HIGH-SPEED SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • Expected speed boost: 10-20x faster!")
    
    print(f"\n🚀 Starting high-speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 HIGH-SPEED RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        print(f"🎯 Speed improvement: {(len(urls)/duration) / 0.1:.1f}x faster than conservative settings!")
        
        # Print summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"high_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        return True, duration, len(urls)/duration
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


async def test_high_speed_medium():
    """Test with high-speed settings on 25 URLs."""
    print("\n🚀 HIGH-SPEED MEDIUM TEST (25 URLs)")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:25]  # First 25 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # MAXIMUM SPEED SETTINGS
    extractor = PerfectContactExtractor(
        batch_size=25,      # Process all 25 URLs in one batch
        max_concurrent=15   # 15 concurrent requests
    )
    
    print(f"⚡ MAXIMUM SPEED SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • This will hit websites HARD and FAST!")
    
    print(f"\n🚀 Starting maximum speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 MAXIMUM SPEED RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        print(f"🎯 Speed improvement: {(len(urls)/duration) / 0.22:.1f}x faster than medium settings!")
        
        # Print summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"maximum_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        return True, duration, len(urls)/duration
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


async def test_insane_speed():
    """Test with INSANE speed settings on 50 URLs."""
    print("\n🔥 INSANE SPEED TEST (50 URLs)")
    print("=" * 40)
    
    # Load practice URLs
    try:
        df = pd.read_csv('practice_urls.csv')
        urls = df['URL'].dropna().tolist()[:50]  # First 50 URLs
        print(f"✅ Loaded {len(urls)} test URLs")
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return False
    
    # INSANE SPEED SETTINGS
    extractor = PerfectContactExtractor(
        batch_size=50,      # Process all 50 URLs in one batch
        max_concurrent=25   # 25 concurrent requests - INSANE!
    )
    
    print(f"🔥 INSANE SPEED SETTINGS:")
    print(f"   • Batch size: {extractor.batch_size}")
    print(f"   • Max concurrent: {extractor.max_concurrent}")
    print(f"   • WARNING: This will absolutely HAMMER websites!")
    print(f"   • Your internet connection better be ready! 🌪️")
    
    print(f"\n🚀 Starting INSANE speed extraction...")
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n📊 INSANE SPEED RESULTS:")
        print(f"⏱️  Processing time: {duration:.1f} seconds")
        print(f"⚡ Rate: {len(urls)/duration:.2f} URLs/second")
        print(f"🎯 Speed improvement: {(len(urls)/duration) / 0.22:.1f}x faster than medium settings!")
        
        # Calculate projections for your main dataset
        main_dataset_size = 8004
        projected_time = main_dataset_size / (len(urls)/duration)
        
        print(f"\n🔮 PROJECTIONS FOR MAIN DATASET ({main_dataset_size} URLs):")
        print(f"   • At this rate: {projected_time/60:.1f} minutes ({projected_time/3600:.1f} hours)")
        print(f"   • That's {8004/(len(urls)/duration):.0f} seconds total!")
        
        # Print summary
        extractor.print_summary(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"insane_speed_test_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        return True, duration, len(urls)/duration
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


async def main():
    """Main high-speed test function."""
    print("🔥 HIGH-SPEED PRACTICE URL TESTING")
    print("=" * 50)
    print("This will test increasingly aggressive speed settings:")
    print("1. High-speed (10 URLs, 10 concurrent)")
    print("2. Maximum speed (25 URLs, 15 concurrent)")
    print("3. INSANE speed (50 URLs, 25 concurrent)")
    print("=" * 50)
    
    speeds = []
    
    try:
        # Test 1: High-speed
        print("TEST 1: High-speed settings")
        success, duration, rate = await test_high_speed_small()
        if success:
            speeds.append(("High-speed (10 URLs)", rate))
            print(f"✅ High-speed test successful! Rate: {rate:.2f} URLs/sec")
        else:
            print("❌ High-speed test failed")
            return
        
        print("\nPress Enter to continue to maximum speed test...")
        input()
        
        # Test 2: Maximum speed
        print("\nTEST 2: Maximum speed settings")
        success, duration, rate = await test_high_speed_medium()
        if success:
            speeds.append(("Maximum speed (25 URLs)", rate))
            print(f"✅ Maximum speed test successful! Rate: {rate:.2f} URLs/sec")
        else:
            print("❌ Maximum speed test failed")
            return
        
        print("\nPress Enter to continue to INSANE speed test...")
        input()
        
        # Test 3: INSANE speed
        print("\nTEST 3: INSANE speed settings")
        success, duration, rate = await test_insane_speed()
        if success:
            speeds.append(("INSANE speed (50 URLs)", rate))
            print(f"✅ INSANE speed test successful! Rate: {rate:.2f} URLs/sec")
        
        # Summary
        print(f"\n🏆 SPEED TEST SUMMARY:")
        print("=" * 50)
        for test_name, rate in speeds:
            print(f"   • {test_name}: {rate:.2f} URLs/second")
        
        if len(speeds) > 1:
            best_rate = max(speeds, key=lambda x: x[1])
            print(f"\n🥇 FASTEST: {best_rate[0]} at {best_rate[1]:.2f} URLs/second")
            
            # Final projection
            main_dataset_size = 8004
            time_for_main = main_dataset_size / best_rate[1]
            print(f"\n🎯 MAIN DATASET PROJECTION:")
            print(f"   • {main_dataset_size} URLs at {best_rate[1]:.2f} URLs/sec")
            print(f"   • Total time: {time_for_main/60:.1f} minutes ({time_for_main/3600:.1f} hours)")
            print(f"   • That's BLAZING FAST! 🔥")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
